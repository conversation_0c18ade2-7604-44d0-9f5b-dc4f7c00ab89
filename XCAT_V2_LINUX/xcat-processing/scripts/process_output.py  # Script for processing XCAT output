import os
import numpy as np
import vtk
from vtk.util.numpy_support import vtk_to_numpy, numpy_to_vtk
from src.utils.file_operations import getParValues

def process_xcat_output(output_case, output_vti_phantom):
    frames_0 = np.sort(next(os.walk(output_case))[2])
    frames_img = [frame for frame in frames_0 if frame.endswith('bin')]
    
    counterf = 1
    for f_sel in frames_img:
        print(f'Processing frame {counterf} from folder {output_case}')

        x = np.fromfile(f'{output_case}/{f_sel}', 'float32')
        image_xc = np.real(np.reshape(x, (arr_size, arr_size, sl_end - sl_start + 1)))
        
        out_img_shape = (arr_size, arr_size, sl_end - sl_start + 1)
        image = np.zeros(out_img_shape)
        for i in range(image_xc.shape[0]):
            image[:, :, image_xc.shape[0] - i - 1] = image_xc[i, :, :]

        vti_image_xcat = vtk.vtkImageData()
        vti_image_xcat.SetDimensions(out_img_shape)
        vti_image_xcat.SetSpacing((res_plane, res_plane, res_slice))
        vti_image_xcat.SetOrigin(orig_img)

        linear_array = image.reshape(-1, 1, order="F")
        vtk_array = numpy_to_vtk(linear_array)
        vtk_array.SetName('labels')
        vti_image_xcat.GetPointData().AddArray(vtk_array)
        vti_image_xcat.Modified()

        writer = vtk.vtkXMLImageDataWriter()
        writer.SetFileName(f'{output_vti_phantom}/Image_{counterf:02d}.vti')
        writer.SetInputData(vti_image_xcat)
        writer.Write()

        counterf += 1

if __name__ == "__main__":
    output_case = '/path/to/xcat/output'
    output_vti_phantom = '/path/to/vti/output'
    process_xcat_output(output_case, output_vti_phantom)