import os
from src.xcat_processor import run_MRXCAT
from src.config.default_params import DEFAULT_PARAM_FILE

def main():
    param_file = DEFAULT_PARAM_FILE
    target_heart_phases = 30  # Example value, adjust as needed
    par_file_folder = os.path.dirname(param_file)
    output_case = './output'  # Specify your output directory

    run_MRXCAT(param_file, target_heart_phases, par_file_folder, output_case)

if __name__ == "__main__":
    main()