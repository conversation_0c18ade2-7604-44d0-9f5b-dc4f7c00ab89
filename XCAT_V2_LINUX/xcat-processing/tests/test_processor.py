import unittest
from src.xcat_processor import run_MRXCAT

class TestMRXCATProcessing(unittest.TestCase):

    def setUp(self):
        self.param_file = 'test_params.par'
        self.target_heart_phases = 30
        self.par_file_folder = './test_params'
        self.output_case = './test_output'

    def test_run_mrxcat(self):
        # Test the main processing function
        result = run_MRXCAT(self.param_file, self.target_heart_phases, self.par_file_folder, self.output_case)
        self.assertIsNone(result)  # Assuming the function returns None on success

    def tearDown(self):
        # Clean up any generated files or directories after tests
        pass

if __name__ == '__main__':
    unittest.main()