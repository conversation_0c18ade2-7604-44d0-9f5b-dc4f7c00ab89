import unittest
from src.utils.file_operations import read_param_file, write_param_file
from src.utils.transformations import rotate_coordinates

class TestUtils(unittest.TestCase):

    def test_read_param_file(self):
        # Test reading a parameter file
        param_file = 'test_params.par'
        expected_data = 'mode = 0\nmotion_option = 2\n'
        with open(param_file, 'w') as f:
            f.write(expected_data)
        
        data = read_param_file(param_file)
        self.assertEqual(data, expected_data)
    
    def test_write_param_file(self):
        # Test writing to a parameter file
        param_file = 'test_write_params.par'
        data_to_write = 'mode = 4\nmotion_option = 1\n'
        write_param_file(param_file, data_to_write)
        
        with open(param_file, 'r') as f:
            data = f.read()
        
        self.assertEqual(data, data_to_write)

    def test_rotate_coordinates(self):
        # Test rotation of coordinates
        coordinates = (1, 0, 0)
        angle = 90  # degrees
        expected_rotated = (0, 1, 0)  # Expected result after 90-degree rotation around Z-axis
        rotated = rotate_coordinates(coordinates, angle)
        
        self.assertAlmostEqual(rotated[0], expected_rotated[0], places=5)
        self.assertAlmostEqual(rotated[1], expected_rotated[1], places=5)
        self.assertAlmostEqual(rotated[2], expected_rotated[2], places=5)

if __name__ == '__main__':
    unittest.main()