from setuptools import setup, find_packages

setup(
    name='xcat-processing',
    version='0.1.0',
    author='Your Name',
    author_email='<EMAIL>',
    description='A package for processing XCAT models and generating outputs.',
    packages=find_packages(where='src'),
    package_dir={'': 'src'},
    install_requires=[
        'numpy',
        'vtk',
    ],
    classifiers=[
        'Programming Language :: Python :: 3',
        'License :: OSI Approved :: MIT License',
        'Operating System :: OS Independent',
    ],
    python_requires='>=3.6',
)