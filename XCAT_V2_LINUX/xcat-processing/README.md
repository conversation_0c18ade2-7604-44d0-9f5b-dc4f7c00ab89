# XCAT Processing Project

This project provides a framework for processing XCAT (Extended Cardiac-Torso) models. It includes utilities for file handling, parameter management, and output processing, as well as scripts for running the XCAT model and converting its output.

## Project Structure

```
xcat-processing
├── src
│   ├── xcat_processor.py  # Main processing module
│   ├── utils
│   │   ├── __init__.py
│   │   ├── file_operations.py  # File handling utilities
│   │   └── transformations.py  # Rotation and coordinate transforms
│   └── config
│       ├── __init__.py
│       └── default_params.py  # Default parameters for XCAT
├── scripts
│   ├── run_xcat.py  # Simplified script for running XCAT
│   └── process_output.py  # Script for processing XCAT output
├── tests
│   ├── __init__.py
│   ├── test_processor.py
│   └── test_utils.py
├── requirements.txt
├── setup.py
└── README.md
```

## Installation

To set up the project, clone the repository and install the required dependencies:

```bash
git clone <repository-url>
cd xcat-processing
pip install -r requirements.txt
```

## Usage

To run the XCAT model, use the provided script:

```bash
python scripts/run_xcat.py <param_file> <target_heart_phases> <par_file_folder> <output_case>
```

This command will generate the XCAT output based on the specified parameters.

## Output Processing

After running the XCAT model, you can process the output using:

```bash
python scripts/process_output.py <output_directory>
```

This script will convert the binary output files to VTI format and handle displacement maps.

## Testing

Unit tests are provided to ensure the functionality of the processing module and utility functions. To run the tests, use:

```bash
pytest tests/
```

## Contributing

Contributions are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for details.