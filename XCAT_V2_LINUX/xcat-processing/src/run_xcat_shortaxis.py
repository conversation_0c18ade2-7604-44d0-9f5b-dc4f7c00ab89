import os
import shutil
import argparse
import numpy as np

def run_xcat_shortaxis(param_file, output_dir, use_config=False):
    """
    Run XCAT model with short-axis view settings
    
    Args:
        param_file: Path to the parameter file
        output_dir: Directory for output
        use_config: Whether to use a config file (appears unnecessary)
    """
    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    
    # Parameters for short-axis view
    xrot = 130.0
    yrot = 35.0
    zrot = 240.0

    # Path to XCAT executable
    xcat_executable = "/home/<USER>/XCAT_Project/XCAT_V2_LINUX/dxcat2_linux_64bit"
    
    # Build command with the simpler pattern that worked before
    cmd = "{} {} --mode 0 --phan_rotx {} --phan_roty {} --phan_rotz {} {}".format(
        xcat_executable, param_file, xrot, yrot, zrot, output_dir)
        
    print("Executing: {}".format(cmd))
    exit_code = os.system(cmd)
    
    # Check if execution was successful
    if exit_code != 0:
        print("Warning: XCAT execution may have encountered issues (exit code: {})".format(exit_code))
        
    # Copy parameter file to output directory for reference
    param_basename = os.path.basename(param_file)
    shutil.copyfile(param_file, os.path.join(output_dir, param_basename))
    
    print("XCAT processing completed. Output saved to: {}".format(output_dir))
    return True

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Run XCAT model with short-axis view settings')
    parser.add_argument('param_file', help='Path to the parameter file (e.g., male93.samp.par)')
    parser.add_argument('output_dir', help='Directory for output')
    args = parser.parse_args()
    
    run_xcat_shortaxis(args.param_file, args.output_dir)