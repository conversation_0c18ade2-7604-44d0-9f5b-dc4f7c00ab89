def run_MRXCAT(param_file, target_heart_phases, par_file_folder, output_case):
    import os
    import shutil
    import numpy as np
    import vtk
    from vtk.util.numpy_support import vtk_to_numpy, numpy_to_vtk
    from utils.file_operations import read_param_file, write_param_file
    from utils.transformations import apply_rotation

    base_name = 'Image'
    param_filev_beating = param_file.rstrip('.par') + '_beating_vectors.par'
    base_name_beating = base_name + '_beating'
    param_filev_breathing = param_file.rstrip('.par') + '_breathing_vectors.par'
    base_name_breathing = base_name + '_breathing'

    output_XCAT = os.path.join(output_case, 'XCAT_output')
    output_VTI_phantom = os.path.join(output_case, 'XCAT_vti')

    os.makedirs(output_case, exist_ok=True)
    os.makedirs(output_XCAT, exist_ok=True)
    os.makedirs(output_VTI_phantom, exist_ok=True)

    xrot, yrot, zrot = 130.0, 35.0, 240.0

    data = read_param_file(os.path.join(par_file_folder, param_file))
    data[0] = 'mode = 0\n'
    data[6] = 'motion_option = 2\n'
    data[9] = 'time_per_frame = %1.2f\n' % (1.0 / target_heart_phases)
    data[10] = 'out_frames = %d\n' % (target_heart_phases)
    write_param_file(os.path.join(output_case, param_file), data)

    os.chdir("XCAT_core")
    os.system('dxcat2.exe %s --phan_rotx %.1f --phan_roty %.1f --phan_rotz %.1f %s/%s' % (
        os.path.join(output_case, param_file), xrot, yrot, zrot, output_XCAT, base_name))
    os.chdir("..")
    shutil.copyfile(os.path.join(output_case, param_file), os.path.join(output_XCAT, param_file))

    data[0] = 'mode = 4\n'
    data[6] = 'motion_option = 0\n'
    write_param_file(os.path.join(output_case, param_filev_beating), data)

    os.chdir("XCAT_core")
    os.system('dxcat2.exe %s --phan_rotx %.1f --phan_roty %.1f --phan_rotz %.1f %s/%s' % (
        os.path.join(output_case, param_filev_beating), xrot, yrot, zrot, output_XCAT, base_name_beating))
    os.chdir("..")
    shutil.copyfile(os.path.join(output_case, param_filev_beating), os.path.join(output_XCAT, param_filev_beating))

    data[6] = 'motion_option = 1\n'
    write_param_file(os.path.join(output_case, param_filev_breathing), data)

    os.chdir("XCAT_core")
    os.system('dxcat2.exe %s --phan_rotx %.1f --phan_roty %.1f --phan_rotz %.1f %s/%s' % (
        os.path.join(output_case, param_filev_breathing), xrot, yrot, zrot, output_XCAT, base_name_breathing))
    os.chdir("..")
    shutil.copyfile(os.path.join(output_case, param_filev_breathing), os.path.join(output_XCAT, param_filev_breathing))

    frames_img = [f for f in sorted(os.listdir(output_XCAT)) if f.endswith('.bin')]
    for counterf, f_sel in enumerate(frames_img, start=1):
        x = np.fromfile(os.path.join(output_XCAT, f_sel), 'float32')
        image_xc = np.real(np.reshape(x, (sl_end - sl_start + 1, arr_size, arr_size)))
        # Further processing...

    # Additional processing and VTI conversion logic here...