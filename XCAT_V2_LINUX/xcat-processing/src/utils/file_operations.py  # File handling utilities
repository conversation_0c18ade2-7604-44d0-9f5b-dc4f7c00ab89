def read_parameter_file(file_path):
    with open(file_path, 'r') as file:
        return file.readlines()

def write_parameter_file(file_path, data):
    with open(file_path, 'w') as file:
        file.writelines(data)

def create_directory(directory_path):
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)

def copy_file(source, destination):
    shutil.copyfile(source, destination)

def list_files_in_directory(directory_path, extension=None):
    if extension:
        return [f for f in os.listdir(directory_path) if f.endswith(extension)]
    return os.listdir(directory_path)