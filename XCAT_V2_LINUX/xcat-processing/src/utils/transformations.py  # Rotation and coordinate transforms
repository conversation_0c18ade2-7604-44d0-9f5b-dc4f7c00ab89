def rotate_point(point, angles):
    import numpy as np

    # Convert angles from degrees to radians
    angles = np.radians(angles)

    # Rotation matrices around the x, y, and z axes
    Rx = np.array([[1, 0, 0],
                   [0, np.cos(angles[0]), -np.sin(angles[0])],
                   [0, np.sin(angles[0]), np.cos(angles[0])]])

    Ry = np.array([[np.cos(angles[1]), 0, np.sin(angles[1])],
                   [0, 1, 0],
                   [-np.sin(angles[1]), 0, np.cos(angles[1])]])

    Rz = np.array([[np.cos(angles[2]), -np.sin(angles[2]), 0],
                   [np.sin(angles[2]), np.cos(angles[2]), 0],
                   [0, 0, 1]])

    # Combined rotation matrix
    R = Rz @ Ry @ Rx

    # Rotate the point
    rotated_point = R @ np.array(point)
    return rotated_point.tolist()

def translate_point(point, translation_vector):
    return [point[i] + translation_vector[i] for i in range(len(point))]

def scale_point(point, scale_factor):
    return [coord * scale_factor for coord in point]