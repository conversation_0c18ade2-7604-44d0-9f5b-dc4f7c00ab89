#!/usr/bin/env python3
"""
Multi-timeframe MRXCAT processing script
Processes cardiac VTI files across multiple time frames
"""

import os
import sys
import numpy as np
import vtk
from vtk.util.numpy_support import vtk_to_numpy, numpy_to_vtk
from vtkmodules.util import numpy_support
import scipy.ndimage as ndimage
from scipy import ndimage
from scipy.ndimage import zoom, gaussian_filter
from scipy.optimize import lsq_linear
import time
from multiprocessing import Pool, cpu_count
from joblib import Parallel, delayed
from tqdm import tqdm
import datetime

# Add local modules to path
sys.path.insert(0, './myWarpFunctions')
sys.path.insert(0, './myTexturizer')

# Import local modules
import myMorphingFunctions as MMF
from texturizationFunctions import defineTissueProperties, defineTissuePropertiesMRXCAT, fixLVTexture, textureReport, XCATProperties

# Workaround for NumPy/VTK compatibility issue
if not hasattr(np, 'bool'):
    np.bool = np.bool_

# Configuration
SUBJECT_ID = "female_151"
SUBJECT_TYPE = "female"
SUBJECT_NUMBER = "151"
TIME_FRAMES = [1, 2, 3, 4, 5]  # List of time frames to process
SUBFOLDER = 'cine'

# Base paths
BASE_DATA_PATH = "/home/<USER>/XCAT_Project/output"
BASE_OUTPUT_PATH = "/home/<USER>/XCAT_Project/outputData"

# Processing flags
runWarper = True
runTexturizer = True
runConverter = True
process_all_slices = True
n_jobs = 8

# Network path for texturizer
network_path = '/home/<USER>/XCAT_Project/myTexturizer/LAXsegnet'

# B0 field path
b0_map_path = '/home/<USER>/XCAT_Project/output/male_169/male_169_field_Hz_20250610_211706.npy'

class myLabels:
    def __init__(self, LV_wall, RV_wall, LA_wall, RA_wall, LV_blood, RV_blood, LA_blood, RA_blood,
                 body, muscle, brain, sinus, liver, gall_bladder, right_lung, left_lung,
                 esophagus, esophagus_contents, laryngopharynx, stomach_wall, stomach_contents,
                 pancreas, right_kidney_cortex, right_kidney_medulla, left_kidney_cortex,
                 left_kidney_medulla, adrenal, right_renal_pelvis, left_renal_pelvis,
                 spleen, ribs, cortical_bone, spine, spinal_cord, bone_marrow, arteries,
                 veins, bladder, prostate, ascending_intestine, transverse_intestine,
                 descending_intestine, small_intestine, rectum, seminal_vesicles,
                 vas_deferens, testes, epididymis, ejaculatory_duct, pericardium,
                 cartilage, intestine_air, ureter, urethra, lymph, lymph_abnormal,
                 trachea_bronchi, airways, thyroid, thymus):
        self.LV_wall = LV_wall
        self.RV_wall = RV_wall
        self.LA_wall = LA_wall
        self.RA_wall = RA_wall
        self.LV_blood = LV_blood
        self.RV_blood = RV_blood
        self.LA_blood = LA_blood
        self.RA_blood = RA_blood
        self.body = body
        self.muscle = muscle
        self.brain = brain
        self.sinus = sinus
        self.liver = liver
        self.gall_bladder = gall_bladder
        self.right_lung = right_lung
        self.left_lung = left_lung
        self.esophagus = esophagus
        self.esophagus_contents = esophagus_contents
        self.laryngopharynx = laryngopharynx
        self.stomach_wall = stomach_wall
        self.stomach_contents = stomach_contents
        self.pancreas = pancreas
        self.right_kidney_cortex = right_kidney_cortex
        self.right_kidney_medulla = right_kidney_medulla
        self.left_kidney_cortex = left_kidney_cortex
        self.left_kidney_medulla = left_kidney_medulla
        self.adrenal = adrenal
        self.right_renal_pelvis = right_renal_pelvis
        self.left_renal_pelvis = left_renal_pelvis
        self.spleen = spleen
        self.ribs = ribs
        self.cortical_bone = cortical_bone
        self.spine = spine
        self.spinal_cord = spinal_cord
        self.bone_marrow = bone_marrow
        self.arteries = arteries
        self.veins = veins
        self.bladder = bladder
        self.prostate = prostate
        self.ascending_intestine = ascending_intestine
        self.transverse_intestine = transverse_intestine
        self.descending_intestine = descending_intestine
        self.small_intestine = small_intestine
        self.rectum = rectum
        self.seminal_vesicles = seminal_vesicles
        self.vas_deferens = vas_deferens
        self.testes = testes
        self.epididymis = epididymis
        self.ejaculatory_duct = ejaculatory_duct
        self.Peri = pericardium
        self.cartilage = cartilage
        self.intestine_air = intestine_air
        self.ureter = ureter
        self.urethra = urethra
        self.lymph = lymph
        self.lymph_abnormal = lymph_abnormal
        self.trachea_bronchi = trachea_bronchi
        self.airways = airways
        self.thyroid = thyroid
        self.thymus = thymus

maskLabels = myLabels(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 
                     17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 
                     31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 
                     45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 
                     65, 66)

def parentVTI(img_slice, num_slices, size):
    """Create a parent VTI object containing the given slice."""
    import vtk
    from vtk.util.numpy_support import vtk_to_numpy, numpy_to_vtk
    
    parent_img = vtk.vtkImageData()
    dims = img_slice.GetDimensions()
    spacing = img_slice.GetSpacing()
    origin = img_slice.GetOrigin()
    
    parent_img.SetDimensions(dims[0], dims[1], num_slices)
    parent_img.SetSpacing(spacing)
    parent_img.SetOrigin(origin)
    
    pd = img_slice.GetPointData()
    for i in range(pd.GetNumberOfArrays()):
        array_name = pd.GetArrayName(i)
        array_data = vtk_to_numpy(pd.GetArray(i))
        
        if len(array_data.shape) == 1:
            extended_array = np.tile(array_data, num_slices)
        else:
            extended_array = np.tile(array_data, (num_slices, 1))
            
        vtk_array = numpy_to_vtk(extended_array)
        vtk_array.SetName(array_name)
        parent_img.GetPointData().AddArray(vtk_array)
    
    return parent_img

def voxelNumpyToVTK(BP_fill, mapx, mapy):
    """Create a VTK image data object from a NumPy array and mapping coordinates."""
    import vtk
    from vtk.util.numpy_support import numpy_to_vtk
    
    img = vtk.vtkImageData()
    img.SetDimensions(BP_fill.shape[0], BP_fill.shape[1], 1)
    img.SetSpacing(1.0, 1.0, 1.0)
    img.SetOrigin(0.0, 0.0, 0.0)
    
    BP_fill_flat = BP_fill.reshape(-1, order='F')
    BP_vtk = numpy_to_vtk(BP_fill_flat)
    BP_vtk.SetName('labels')
    img.GetPointData().AddArray(BP_vtk)
    
    if mapx is not None and mapy is not None:
        mapx_flat = mapx.reshape(-1, order='F')
        mapx_vtk = numpy_to_vtk(mapx_flat)
        mapx_vtk.SetName('mapx')
        img.GetPointData().AddArray(mapx_vtk)
        
        mapy_flat = mapy.reshape(-1, order='F')
        mapy_vtk = numpy_to_vtk(mapy_flat)
        mapy_vtk.SetName('mapy')
        img.GetPointData().AddArray(mapy_vtk)
    
    return img

# Add functions to MMF
MMF.parentVTI = parentVTI
MMF.voxelNumpyToVTK = voxelNumpyToVTK

def check_slice_completion(slice_idx, results_folder, runWarper, runTexturizer):
    """Check if specified slice is already processed."""
    slice_results_folder = f"{results_folder}/slice_{slice_idx:03d}"
    slice_tissue_folder = f"{results_folder}/Tissue_properties_initial_frame/slice_{slice_idx:03d}"
    
    required_files = []
    
    if runWarper:
        model_file = os.path.join(slice_results_folder, "Model.vti")
        required_files.append(model_file)
        
    if runTexturizer:
        tissue_files = [
            os.path.join(slice_tissue_folder, "PD.vti"),
            os.path.join(slice_tissue_folder, "T1.vti"), 
            os.path.join(slice_tissue_folder, "T2.vti"),
            os.path.join(slice_tissue_folder, "T2s.vti")
        ]
        required_files.extend(tissue_files)
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            return False
        if os.path.getsize(file_path) == 0:
            return False
            
    return True

def process_single_slice_fixed(slice_idx, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                              results_folder, warped_images, runWarper, runTexturizer, network_path,
                              voxelNumpyToVTK, parentVTI, force_reprocess=False):
    """Process a single slice with fixed MMF import issues."""
    try:
        if not force_reprocess and check_slice_completion(slice_idx, results_folder, runWarper, runTexturizer):
            print(f"✅ Slice {slice_idx} already processed, skipping...")
            return True
            
        print(f"Processing slice {slice_idx}...")

        # Force set Python path
        import sys
        import os
        
        paths_to_add = [
            '/home/<USER>/XCAT_Project',
            '/home/<USER>/XCAT_Project/myWarpFunctions',
            '/home/<USER>/XCAT_Project/myTexturizer'
        ]
        
        for path in paths_to_add:
            if path not in sys.path:
                sys.path.insert(0, path)
        
        os.environ['PYTHONPATH'] = ':'.join(paths_to_add)
        
        try:
            import myMorphingFunctions as MMF
            from texturizationFunctions import defineTissueProperties, fixLVTexture
        except ImportError as e:
            try:
                import importlib.util
                
                mmf_path = '/home/<USER>/XCAT_Project/myWarpFunctions/myMorphingFunctions.py'
                spec = importlib.util.spec_from_file_location("myMorphingFunctions", mmf_path)
                MMF = importlib.util.module_from_spec(spec)
                sys.modules["myMorphingFunctions"] = MMF
                spec.loader.exec_module(MMF)
                
                tex_path = '/home/<USER>/XCAT_Project/myTexturizer/texturizationFunctions.py'
                spec = importlib.util.spec_from_file_location("texturizationFunctions", tex_path)
                tex_module = importlib.util.module_from_spec(spec)
                sys.modules["texturizationFunctions"] = tex_module
                spec.loader.exec_module(tex_module)
                
                defineTissueProperties = tex_module.defineTissueProperties
                fixLVTexture = tex_module.fixLVTexture
            except Exception as e2:
                raise ImportError(f"Cannot import required modules: {e2}")

        # Create slice-specific output directories
        slice_results_folder = f"{results_folder}/slice_{slice_idx:03d}"
        slice_warped_images = f"{warped_images}/slice_{slice_idx:03d}"
        slice_tissue_folder = f"{results_folder}/Tissue_properties_initial_frame/slice_{slice_idx:03d}"
        
        for folder in [slice_results_folder, slice_warped_images, slice_tissue_folder]:
            if not os.path.exists(folder):
                os.makedirs(folder, exist_ok=True)
        
        if runWarper:
            slice_data = np.zeros((dim[0], dim[1], 1))
            slice_data[:,:,0] = labels[:,:,slice_idx]
            
            data_input_slice, Target_mask_slice, BP_fill, mapx0, mapy0, centerBox_x, centerBox_y = MMF.warpSlice(
                Target_mask, data_input, slice_idx, maskLabels, True)
            
            # VTK processing
            MMF.voxelNumpyToVTK = voxelNumpyToVTK
            MMF.parentVTI = parentVTI

            img_ref = MMF.vtkSliceImage(data_input_slice, spacing, 
                                       [orig_image[0], orig_image[1], orig_image[2] + slice_idx*spacing[2]])
            img_ref = MMF.addArrayToVtk(img_ref, data_input_slice, 'labels', False)
            img_ref = MMF.addArrayToVtk(img_ref, Target_mask_slice, 'LV_mask', False)
            
            model_path = os.path.join(slice_results_folder, "Model.vti")
            MMF.saveVTI(img_ref, model_path)
            par_img = img_ref
            
            if runTexturizer:
                PD0, T10, T20, T2s0 = defineTissueProperties(par_img, network_path)
                
                textureLV = False
                if not textureLV:
                    PD0, T10, T20, T2s0, LV_prop_vals = fixLVTexture(
                        data_input_slice, PD0, T10, T20, T2s0, 
                        maskLabels, which_prop='meanLV'
                    )
                
                arrays_to_remove = ['dx_beating', 'dy_beating', 'dz_beating', 
                                   'dx_breathing', 'dy_breathing', 'dz_breathing', 
                                   'maskValues', 'parameters']
                for arr_n in arrays_to_remove:
                    if par_img.GetPointData().GetArray(arr_n):
                        par_img.GetPointData().RemoveArray(arr_n)
                        par_img.Modified()

                par_img = MMF.addArrayToVtk(par_img, PD0, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/PD.vti")
                
                par_img = MMF.addArrayToVtk(par_img, T10, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/T1.vti")
                
                par_img = MMF.addArrayToVtk(par_img, T20, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/T2.vti")
                
                par_img = MMF.addArrayToVtk(par_img, T2s0, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/T2s.vti")
                
                print(f"Slice {slice_idx} tissue properties:")
                print(f"  PD range: {np.min(PD0):.2f}-{np.max(PD0):.2f}")
                print(f"  T1 range: {np.min(T10):.2f}-{np.max(T10):.2f}")
                print(f"  T2 range: {np.min(T20):.2f}-{np.max(T20):.2f}")
                print(f"  T2* range: {np.min(T2s0):.2f}-{np.max(T2s0):.2f}")
                
        print(f"✅ Successfully processed slice {slice_idx}")
        return True
        
    except Exception as e:
        print(f"❌ Error processing slice {slice_idx}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def run_parallel_processing(data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                           results_folder, warped_images, runWarper, runTexturizer, network_path,
                           z_indices, voxelNumpyToVTK, parentVTI, n_jobs=4, check_existing=True, 
                           force_reprocess=False):
    """Run parallel processing with automatic retry and file checking."""
    print(f"🚀 Starting parallel processing of {len(z_indices)} slices using {n_jobs} processes")
    
    if check_existing and not force_reprocess:
        print("\n📋 Checking existing files...")
        unprocessed_slices = []
        completed_slices = []
        
        for slice_idx in z_indices:
            if check_slice_completion(slice_idx, results_folder, runWarper, runTexturizer):
                completed_slices.append(slice_idx)
            else:
                unprocessed_slices.append(slice_idx)
        
        print(f"   Completed: {len(completed_slices)} slices")
        print(f"   To process: {len(unprocessed_slices)} slices")
        
        if not unprocessed_slices:
            print("🎉 All slices already completed!")
            return [True] * len(z_indices)
            
        z_indices_to_process = unprocessed_slices
    else:
        z_indices_to_process = z_indices
        completed_slices = []
    
    start_time = time.time()
    
    if z_indices_to_process:
        print(f"\n📋 Testing single slice to verify environment...")
        test_slice = sorted(z_indices_to_process)[0]
        test_result = process_single_slice_fixed(
            test_slice, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
            results_folder, warped_images, runWarper, runTexturizer, network_path,
            voxelNumpyToVTK, parentVTI, force_reprocess
        )
        
        if not test_result:
            print("❌ Test slice failed! Check environment configuration.")
            return []
        
        print("✅ Test successful! Starting parallel processing...")
        
        results = Parallel(n_jobs=n_jobs, verbose=10)(
            delayed(process_single_slice_fixed)(
                slice_idx, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                results_folder, warped_images, runWarper, runTexturizer, network_path,
                voxelNumpyToVTK, parentVTI, force_reprocess
            ) for slice_idx in sorted(z_indices_to_process)
        )
    else:
        results = []
    
    total_time = time.time() - start_time
    total_slices = len(z_indices)
    processed_slices = len(z_indices_to_process)
    successful_processed = sum(1 for r in results if r) if results else 0
    already_completed = len(completed_slices)
    total_successful = successful_processed + already_completed
    
    print(f"\n🎉 === Parallel processing complete ===")
    print(f"📊 Statistics:")
    print(f"   Total time: {total_time/60:.1f} minutes")
    print(f"   Total slices: {total_slices}")
    print(f"   Already completed: {already_completed} (skipped)")
    print(f"   Newly processed: {processed_slices}")
    print(f"   Newly successful: {successful_processed}/{processed_slices}")
    print(f"   Total successful: {total_successful}/{total_slices}")
    print(f"   Total success rate: {total_successful/total_slices*100:.1f}%")
    
    all_results = []
    processed_idx = 0
    for slice_idx in z_indices:
        if slice_idx in completed_slices:
            all_results.append(True)
        else:
            all_results.append(results[processed_idx] if processed_idx < len(results) else False)
            processed_idx += 1
    
    return all_results

def check_binary_conversion_completion(slice_output_folder_bin, runTexturizer=True):
    """Check if binary conversion is complete for a slice."""
    if not os.path.exists(slice_output_folder_bin):
        return False
    
    required_files = []
    
    labels_file = os.path.join(slice_output_folder_bin, "labels.bin")
    required_files.append(labels_file)
    
    if runTexturizer:
        tissue_properties = ["pd", "t1", "t2", "t2s"]
        for prop in tissue_properties:
            prop_file = os.path.join(slice_output_folder_bin, prop, "value.bin")
            required_files.append(prop_file)
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            return False
        if os.path.getsize(file_path) == 0:
            return False
            
    return True

def vti2Bin_v2(input_folder, output_folder, target_size):
    """Convert VTI files to binary format."""
    print(f"Converting VTI files from {input_folder} to binary in {output_folder}")
    
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    model_file = os.path.join(input_folder, "Model.vti")
    if os.path.exists(model_file):
        reader = vtk.vtkXMLImageDataReader()
        reader.SetFileName(model_file)
        reader.Update()
        model_data = reader.GetOutput()
        
        if model_data.GetPointData().HasArray('labels'):
            label_array = vtk_to_numpy(model_data.GetPointData().GetArray('labels'))
            label_dims = model_data.GetDimensions()
            labels = label_array.reshape(label_dims[0], label_dims[1], label_dims[2], order='F')
            
            if labels.shape[0] != target_size[0] or labels.shape[1] != target_size[1]:
                from scipy.ndimage import zoom
                zoom_factor = (target_size[0]/labels.shape[0], 
                            target_size[1]/labels.shape[1],
                            target_size[2]/labels.shape[2])
                labels = zoom(labels, zoom_factor, order=0)
            
            labels = labels.astype(np.uint8)
            labels.tofile(os.path.join(output_folder, "labels.bin"))
            print(f"✓ Saved labels.bin ({labels.shape})")
        else:
            print("✗ No 'labels' array found in Model.vti")
    else:
        print(f"✗ Model file not found: {model_file}")
        
    tissue_folder = os.path.join(os.path.dirname(input_folder), "Tissue_properties_initial_frame", os.path.basename(input_folder))
    
    for prop in ["PD", "T1", "T2", "T2s"]:
        prop_output_folder = os.path.join(output_folder, prop.lower())
        if not os.path.exists(prop_output_folder):
            os.makedirs(prop_output_folder)
        
        prop_file = os.path.join(input_folder, f"{prop}.vti")
        
        if not os.path.exists(prop_file):
            prop_file = os.path.join(tissue_folder, f"{prop}.vti")
        
        if os.path.exists(prop_file):
            reader = vtk.vtkXMLImageDataReader()
            reader.SetFileName(prop_file)
            reader.Update()
            prop_data = reader.GetOutput()
            
            array_name = None
            if prop_data.GetPointData().HasArray('parameters'):
                array_name = 'parameters'
            elif prop_data.GetPointData().HasArray(prop):
                array_name = prop
                
            if array_name:
                prop_array = vtk_to_numpy(prop_data.GetPointData().GetArray(array_name))
                prop_dims = prop_data.GetDimensions()
                prop_values = prop_array.reshape(prop_dims[0], prop_dims[1], prop_dims[2], order='F')
                
                if prop_values.shape[0] != target_size[0] or prop_values.shape[1] != target_size[1]:
                    from scipy.ndimage import zoom
                    zoom_factor = (target_size[0]/prop_values.shape[0], 
                                target_size[1]/prop_values.shape[1],
                                target_size[2]/prop_values.shape[2])
                    prop_values = zoom(prop_values, zoom_factor, order=1)
                
                prop_values = prop_values.astype(np.float32)
                output_file = os.path.join(prop_output_folder, "value.bin")
                prop_values.tofile(output_file)
                print(f"✓ Saved {prop.lower()}/value.bin ({prop_values.shape})")
            else:
                print(f"✗ No suitable array found in {prop}.vti")
        else:
            print(f"✗ Property file not found: {prop_file}")

def process_time_frame(time_frame):
    """Process a single time frame."""
    print(f"\n{'='*60}")
    print(f"PROCESSING TIME FRAME {time_frame}")
    print(f"{'='*60}")
    
    # Generate paths for this time frame
    data_folder = BASE_DATA_PATH
    subject_folder = f"{data_folder}/{SUBJECT_ID}/{SUBFOLDER}"
    results_folder = f"{BASE_OUTPUT_PATH}/{SUBJECT_ID}/{SUBFOLDER}/frame_{time_frame:02d}"
    output_folder_bin = f"{BASE_OUTPUT_PATH}/{SUBJECT_ID}/{SUBFOLDER}/frame_{time_frame:02d}/bin_output"
    warped_images = f"{BASE_OUTPUT_PATH}/{SUBJECT_ID}/{SUBFOLDER}/frame_{time_frame:02d}/warped_images"
    file_input = f"{subject_folder}/{SUBJECT_ID}_cine.samp_act_{time_frame}.vti"
    
    print(f"Input file: {file_input}")
    print(f"Output folder: {results_folder}")
    
    # Check if file exists
    if not os.path.exists(file_input):
        print(f"❌ Error: File not found at {file_input}")
        available_files = [f for f in os.listdir(subject_folder) if f.endswith('.vti')]
        print(f"Available VTI files: {available_files}")
        return False
    
    print(f"✅ Input file found!")
    
    # Create directories
    for folder in [results_folder, output_folder_bin, warped_images]:
        if not os.path.exists(folder):
            os.makedirs(folder)
    
    if not os.path.exists(results_folder + "/SAX_slices"):
        os.makedirs(results_folder + "/SAX_slices")
    
    if not os.path.exists(results_folder + "/Tissue_properties_initial_frame"):
        os.makedirs(results_folder + "/Tissue_properties_initial_frame")
    
    # Load VTI file
    reader = vtk.vtkXMLImageDataReader()
    reader.SetFileName(file_input)
    reader.Update()
    
    vtiIMG = reader.GetOutput()
    print(f"Loaded time frame {time_frame} successfully!")
    print(f"Image dimensions: {vtiIMG.GetDimensions()}")
    
    # Extract label data
    img_ref = vtiIMG
    origin = img_ref.GetOrigin()
    original_dims = img_ref.GetDimensions()
    original_spacing = img_ref.GetSpacing()
    
    # Swap dimensions so Z is the last dimension
    dim = [original_dims[1], original_dims[2], original_dims[0]]
    spacing = [original_spacing[1], original_spacing[2], original_spacing[0]]
    
    print("Model info:")
    print(f"Origin: {origin}")
    print(f"Dimensions: {dim}")
    print(f"Spacing: {spacing}")
    
    try:
        labels_array = img_ref.GetPointData().GetArray('labels')
        if labels_array is not None:
            labels_original = vtk_to_numpy(labels_array).reshape(original_dims, order='F')
            labels = np.transpose(labels_original, (1, 2, 0))
            data_input = labels.copy()
            print("Successfully loaded labels from file")
            print(f"Labels shape: {labels.shape}")
            print(f"Unique labels: {np.unique(labels)}")
        else:
            raise ValueError("No 'labels' array found")
    except Exception as e:
        print(f"Warning: Could not load 'labels' array from file. Error: {e}")
        print("Creating placeholder labels...")
        labels = np.zeros(dim, dtype=np.uint8)
        data_input = labels.copy()
        print(f"Created placeholder labels with shape: {labels.shape}")
    
    # Create tissue-specific masks
    Target_mask = np.zeros_like(labels, dtype=np.uint8)
    Blood_mask = np.zeros_like(labels, dtype=np.uint8)
    
    wall_labels = [maskLabels.LV_wall, maskLabels.RV_wall]
    blood_labels = [maskLabels.LV_blood, maskLabels.RV_blood]
    
    for label in wall_labels:
        Target_mask[labels == label] = 1
        
    for label in blood_labels:
        Blood_mask[labels == label] = 1
        
    print(f"Wall voxels: {np.sum(Target_mask == 1)}")
    print(f"Blood voxels: {np.sum(Blood_mask == 1)}")
    
    # Find z-indices with cardiac content
    original_z_indices = np.where(np.sum(Target_mask, axis=(0,1)) > 0)[0]
    print(f"Found {len(original_z_indices)} slices containing cardiac structures")
    
    # Extend z_indices
    z_min = max(0, original_z_indices[0] - 50)
    z_max = min(labels.shape[2] - 1, original_z_indices[-1] + 50)
    z_indices = np.arange(z_min, z_max + 1)
    
    print(f"Processing z-range: {z_min} to {z_max} ({len(z_indices)} slices)")
    
    # Process slices
    orig_image = origin
    results = run_parallel_processing(
        data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
        results_folder, warped_images, runWarper, runTexturizer, network_path,
        z_indices, voxelNumpyToVTK, parentVTI, 
        n_jobs=n_jobs,
        check_existing=True,
        force_reprocess=False
    )
    
    # Convert to binary format
    if runConverter:
        print(f"\n🔄 Converting to binary format...")
        
        successful_conversions = 0
        failed_conversions = 0
        
        for SAX_slice_selection in z_indices:
            slice_output_folder_bin = f"{output_folder_bin}/slice_{SAX_slice_selection:03d}"
            slice_results_folder = f"{results_folder}/slice_{SAX_slice_selection:03d}"
            
            if check_binary_conversion_completion(slice_output_folder_bin, runTexturizer):
                successful_conversions += 1
                continue
            
            print(f"Converting slice {SAX_slice_selection} to binary format...")
            
            if not os.path.exists(slice_output_folder_bin):
                os.makedirs(slice_output_folder_bin)
                
            try:
                model_file = os.path.join(slice_results_folder, "Model.vti")
                if not os.path.exists(model_file):
                    print(f"✗ Model.vti not found in {slice_results_folder}")
                    failed_conversions += 1
                else:
                    vti2Bin_v2(slice_results_folder, slice_output_folder_bin, [500, 500, 1])
                    
                    if check_binary_conversion_completion(slice_output_folder_bin, runTexturizer):
                        successful_conversions += 1
                    else:
                        failed_conversions += 1
                        
            except Exception as e:
                print(f"✗ Error converting slice {SAX_slice_selection}: {str(e)}")
                failed_conversions += 1
        
        print(f"\nBinary conversion complete:")
        print(f"   Successful: {successful_conversions}")
        print(f"   Failed: {failed_conversions}")
        print(f"   Success rate: {successful_conversions/(successful_conversions+failed_conversions)*100:.1f}%")
    
    print(f"\n✅ Time frame {time_frame} processing complete!")
    return True

def main():
    """Main function to process all time frames."""
    print(f"Starting multi-timeframe processing for {SUBJECT_ID}")
    print(f"Time frames to process: {TIME_FRAMES}")
    
    successful_frames = 0
    failed_frames = 0
    start_time = time.time()
    
    for time_frame in TIME_FRAMES:
        try:
            result = process_time_frame(time_frame)
            if result:
                successful_frames += 1
            else:
                failed_frames += 1
        except Exception as e:
            print(f"❌ Error processing time frame {time_frame}: {str(e)}")
            failed_frames += 1
    
    total_time = time.time() - start_time
    
    print(f"\n{'='*60}")
    print(f"PROCESSING COMPLETE")
    print(f"{'='*60}")
    print(f"Total time: {total_time/60:.1f} minutes")
    print(f"Successful frames: {successful_frames}/{len(TIME_FRAMES)}")
    print(f"Failed frames: {failed_frames}/{len(TIME_FRAMES)}")
    print(f"Success rate: {successful_frames/len(TIME_FRAMES)*100:.1f}%")

if __name__ == "__main__":
    main()