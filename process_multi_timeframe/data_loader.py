#!/usr/bin/env python3
"""
Data loading and preprocessing functions for XCAT processing pipeline
Handles VTI file loading, label extraction, and mask creation
"""

import os
import numpy as np
import vtk
from vtk.util.numpy_support import vtk_to_numpy
from config import SLICE_EXTENSION_BEFORE, SLICE_EXTENSION_AFTER

def load_vti_file(file_path):
    """Load VTI file and return VTK image data object"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"VTI file not found: {file_path}")
    
    reader = vtk.vtkXMLImageDataReader()
    reader.SetFileName(file_path)
    reader.Update()
    
    vti_img = reader.GetOutput()
    print(f"Loaded VTI file: {file_path}")
    print(f"Image dimensions: {vti_img.GetDimensions()}")
    
    return vti_img

def extract_image_properties(vti_img):
    """Extract basic properties from VTI image"""
    origin = vti_img.GetOrigin()
    original_dims = vti_img.GetDimensions()
    original_spacing = vti_img.GetSpacing()
    
    # Swap dimensions so Z is the last dimension
    dim = [original_dims[1], original_dims[2], original_dims[0]]
    spacing = [original_spacing[1], original_spacing[2], original_spacing[0]]
    
    print("Model info:")
    print(f"Origin: {origin}")
    print(f"Dimensions: {dim}")
    print(f"Spacing: {spacing}")
    
    return {
        'origin': origin,
        'original_dims': original_dims,
        'dim': dim,
        'spacing': spacing
    }

def extract_labels_from_vti(vti_img, properties):
    """Extract label data from VTI image"""
    try:
        labels_array = vti_img.GetPointData().GetArray('labels')
        if labels_array is not None:
            labels_original = vtk_to_numpy(labels_array).reshape(properties['original_dims'], order='F')
            labels = np.transpose(labels_original, (1, 2, 0))
            data_input = labels.copy()
            print("Successfully loaded labels from file")
            print(f"Labels shape: {labels.shape}")
            print(f"Unique labels: {np.unique(labels)}")
            return labels, data_input
        else:
            raise ValueError("No 'labels' array found")
    except Exception as e:
        print(f"Warning: Could not load 'labels' array from file. Error: {e}")
        print("Creating placeholder labels...")
        labels = np.zeros(properties['dim'], dtype=np.uint8)
        data_input = labels.copy()
        print(f"Created placeholder labels with shape: {labels.shape}")
        return labels, data_input

def create_tissue_masks(labels, maskLabels):
    """Create tissue-specific masks from labels"""
    Target_mask = np.zeros_like(labels, dtype=np.uint8)
    Blood_mask = np.zeros_like(labels, dtype=np.uint8)
    
    wall_labels = [maskLabels.LV_wall, maskLabels.RV_wall]
    blood_labels = [maskLabels.LV_blood, maskLabels.RV_blood]
    
    for label in wall_labels:
        Target_mask[labels == label] = 1
        
    for label in blood_labels:
        Blood_mask[labels == label] = 1
        
    print(f"Wall voxels: {np.sum(Target_mask == 1)}")
    print(f"Blood voxels: {np.sum(Blood_mask == 1)}")
    
    return Target_mask, Blood_mask

def find_cardiac_slices(Target_mask):
    """Find Z-indices containing cardiac structures"""
    original_z_indices = np.where(np.sum(Target_mask, axis=(0,1)) > 0)[0]
    print(f"Found {len(original_z_indices)} slices containing cardiac structures")
    
    if len(original_z_indices) == 0:
        print("Warning: No cardiac structures found!")
        return np.array([])
    
    # Extend z_indices by adding slices before and after
    z_min = max(0, original_z_indices[0] - SLICE_EXTENSION_BEFORE)
    z_max = min(Target_mask.shape[2] - 1, original_z_indices[-1] + SLICE_EXTENSION_AFTER)
    extended_z_indices = np.arange(z_min, z_max + 1)
    
    print(f"Original z-range: {original_z_indices[0]} to {original_z_indices[-1]} ({len(original_z_indices)} slices)")
    print(f"Extended z-range: {z_min} to {z_max} ({len(extended_z_indices)} slices)")
    print(f"Added {len(extended_z_indices) - len(original_z_indices)} slices")
    
    return extended_z_indices

def load_and_preprocess_timeframe(file_input, maskLabels):
    """
    Complete data loading and preprocessing pipeline for a time frame
    
    Args:
        file_input: Path to VTI file
        maskLabels: Label definitions object
        
    Returns:
        dict: Contains all processed data and metadata
    """
    print(f"Loading and preprocessing: {file_input}")
    
    # Load VTI file
    vti_img = load_vti_file(file_input)
    
    # Extract image properties
    properties = extract_image_properties(vti_img)
    
    # Extract labels
    labels, data_input = extract_labels_from_vti(vti_img, properties)
    
    # Create tissue masks
    Target_mask, Blood_mask = create_tissue_masks(labels, maskLabels)
    
    # Find cardiac slices
    z_indices = find_cardiac_slices(Target_mask)
    
    return {
        'vti_img': vti_img,
        'properties': properties,
        'labels': labels,
        'data_input': data_input,
        'Target_mask': Target_mask,
        'Blood_mask': Blood_mask,
        'z_indices': z_indices,
        'origin': properties['origin'],
        'dim': properties['dim'],
        'spacing': properties['spacing']
    }

def validate_input_file(file_input):
    """Validate that input file exists and suggest alternatives if not"""
    if not os.path.exists(file_input):
        print(f"❌ Error: File not found at {file_input}")
        
        # Try to find available files in the directory
        directory = os.path.dirname(file_input)
        if os.path.exists(directory):
            available_files = [f for f in os.listdir(directory) if f.endswith('.vti')]
            if available_files:
                print(f"Available VTI files in {directory}:")
                for file in available_files:
                    print(f"  - {file}")
            else:
                print(f"No VTI files found in {directory}")
        else:
            print(f"Directory does not exist: {directory}")
        return False
    
    print(f"✅ Input file found: {file_input}")
    return True

def get_file_info(file_input):
    """Get basic information about the input file"""
    if not os.path.exists(file_input):
        return None
    
    file_size = os.path.getsize(file_input)
    file_size_mb = file_size / (1024 * 1024)
    
    return {
        'path': file_input,
        'size_bytes': file_size,
        'size_mb': file_size_mb,
        'exists': True
    }

def preview_slices_info(z_indices, Target_mask, Blood_mask, max_preview=15):
    """Generate information about slices for preview (without plotting)"""
    if len(z_indices) == 0:
        return []
    
    # Select representative slices
    preview_count = min(max_preview, len(z_indices))
    if preview_count > 1:
        step = (len(z_indices) - 1) // (preview_count - 1)
        indices = [i * step for i in range(preview_count - 1)]
        indices.append(len(z_indices) - 1)
    else:
        indices = [0]
    
    preview_slices = []
    for i in indices:
        z = z_indices[i]
        wall_voxels = np.sum(Target_mask[:,:,z] > 0)
        blood_voxels = np.sum(Blood_mask[:,:,z] > 0)
        has_cardiac = wall_voxels > 0 or blood_voxels > 0
        
        preview_slices.append({
            'z_index': z,
            'wall_voxels': wall_voxels,
            'blood_voxels': blood_voxels,
            'has_cardiac': has_cardiac
        })
    
    return preview_slices
