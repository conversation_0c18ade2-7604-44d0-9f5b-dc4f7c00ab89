XCAT Pipeline Complete Log - 2025-07-15 13:49:12
================================================================================
This log contains ALL output including print statements and logger messages
================================================================================

✅ Module imports successful

================================================================================
STARTING TIME FRAME 1
================================================================================

============================================================
PROCESSING TIME FRAME 1
============================================================
Input file: /home/<USER>/XCAT_Project/output/female_151/cine/female_151_cine.samp_act_1.vti
Output folder: /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01
✅ Input file found: /home/<USER>/XCAT_Project/output/female_151/cine/female_151_cine.samp_act_1.vti
Loading and preprocessing: /home/<USER>/XCAT_Project/output/female_151/cine/female_151_cine.samp_act_1.vti
Loaded VTI file: /home/<USER>/XCAT_Project/output/female_151/cine/female_151_cine.samp_act_1.vti
Image dimensions: (950, 500, 500)
Model info:
Origin: (0.0, 0.0, 0.0)
Dimensions: [500, 500, 950]
Spacing: [0.002, 0.002, 0.002]
Successfully loaded labels from file
Labels shape: (500, 500, 950)
Unique labels: [ 0.  1.  2.  3.  4.  5.  6.  7.  8.  9. 10. 11. 12. 13. 14. 15. 16. 17.
 18. 19. 20. 21. 22. 23. 24. 25. 26. 27. 28. 29. 30. 31. 32. 33. 34. 35.
 36. 37. 38. 40. 41. 42. 43. 44. 50. 51. 52. 53. 54. 57. 58. 59. 60. 61.
 62. 63. 64. 65. 66. 67. 69. 70.]
Wall voxels: 145830
Blood voxels: 13277
Found 131 slices containing cardiac structures
Original z-range: 556 to 805 (131 slices)
Extended z-range: 506 to 855 (350 slices)
Added 219 slices
Processing z-range: 506 to 855 (350 slices)

🔄 Starting slice processing...
🚀 Starting parallel processing of 350 slices using 8 processes

📋 Checking existing files...
   Completed: 0 slices
   To process: 350 slices

📋 Testing single slice to verify environment...
Processing slice 506...
Skipping warpImages3D for slice 506 - missing necessary cardiac structures
/home/<USER>/.conda/envs/xcat/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/home/<USER>/.conda/envs/xcat/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
Slice 506 tissue properties:
  PD range: 35.52-96.79
  T1 range: 159.15-1217.87
  T2 range: 6.90-114.77
  T2* range: 1.00-34.00
✅ Successfully processed slice 506
✅ Test successful! Starting parallel processing...
[Parallel(n_jobs=8)]: Using backend LokyBackend with 8 concurrent workers.
[Parallel(n_jobs=8)]: Done   2 tasks      | elapsed:    9.5s
[Parallel(n_jobs=8)]: Done   9 tasks      | elapsed:   10.1s
/home/<USER>/.local/lib/python3.8/site-packages/joblib/externals/loky/process_executor.py:752: UserWarning: A worker stopped while some jobs were given to the executor. This can be caused by a too short worker timeout or by a memory leak.
  warnings.warn(
[Parallel(n_jobs=8)]: Done  16 tasks      | elapsed:   15.2s
[Parallel(n_jobs=8)]: Done  25 tasks      | elapsed:   24.4s
[Parallel(n_jobs=8)]: Done  34 tasks      | elapsed:   29.2s
[Parallel(n_jobs=8)]: Done  45 tasks      | elapsed:   34.7s
[Parallel(n_jobs=8)]: Done  56 tasks      | elapsed:   40.3s
[Parallel(n_jobs=8)]: Done  69 tasks      | elapsed:   49.5s
[Parallel(n_jobs=8)]: Done  82 tasks      | elapsed:   57.7s
[Parallel(n_jobs=8)]: Done  97 tasks      | elapsed:  1.1min
[Parallel(n_jobs=8)]: Done 112 tasks      | elapsed:  1.3min
[Parallel(n_jobs=8)]: Done 129 tasks      | elapsed:  1.5min
[Parallel(n_jobs=8)]: Done 146 tasks      | elapsed:  1.6min
