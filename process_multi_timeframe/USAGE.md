# XCAT Multi-Timeframe Processing Pipeline - Usage Guide

## Quick Start

The pipeline has been successfully set up and tested. All required dependencies are available in the xcat conda environment.

### Basic Commands

```bash
# Navigate to the processing directory
cd /home/<USER>/XCAT_Project/process_multi_timeframe

# Test the setup (recommended first step)
./run_processing.sh test

# Check processing status
./run_processing.sh status

# Process all time frames
./run_processing.sh all

# Process a specific time frame
./run_processing.sh frame 1
```

## Current Configuration

- **Subject ID**: female_151
- **Time Frames**: [1, 2, 3, 4, 5]
- **Input Path**: `/home/<USER>/XCAT_Project/output/female_151/cine/`
- **Output Path**: `/home/<USER>/XCAT_Project/outputData/female_151/cine/`
- **Processing**: Warper ✅, Texturizer ✅, Converter ✅
- **Parallel Jobs**: 8

## Status Check Results

All input files are available:
- ✅ female_151_cine.samp_act_1.vti
- ✅ female_151_cine.samp_act_2.vti  
- ✅ female_151_cine.samp_act_3.vti
- ✅ female_151_cine.samp_act_4.vti
- ✅ female_151_cine.samp_act_5.vti

No processing has been done yet (all results folders are empty).

## Environment Setup

The pipeline uses the xcat conda environment:
- **Python**: `/home/<USER>/.conda/envs/xcat/bin/python`
- **VTK**: 9.0.3 ✅
- **NumPy**: Available ✅
- **SciPy**: Available ✅
- **joblib**: Available ✅
- **Custom Modules**: myMorphingFunctions, texturizationFunctions ✅

## Processing Workflow

Each time frame goes through three stages:

1. **Warping** (`runWarper=True`)
   - Slice-by-slice processing with morphing functions
   - Creates Model.vti files for each slice
   - Parallel processing across slices

2. **Texturization** (`runTexturizer=True`)
   - Generates tissue properties (PD, T1, T2, T2*)
   - Uses neural network from `/home/<USER>/XCAT_Project/myTexturizer/LAXsegnet`
   - Creates tissue property VTI files

3. **Binary Conversion** (`runConverter=True`)
   - Converts VTI files to binary format
   - Creates structured output for downstream processing
   - Validates conversion completeness

## Output Structure

For each time frame, the pipeline creates:

```
outputData/female_151/cine/frame_XX/
├── slice_XXX/
│   └── Model.vti                    # Warped slice data
├── Tissue_properties_initial_frame/
│   └── slice_XXX/
│       ├── PD.vti                   # Proton density
│       ├── T1.vti                   # T1 relaxation
│       ├── T2.vti                   # T2 relaxation
│       └── T2s.vti                  # T2* relaxation
├── bin_output/
│   └── slice_XXX/
│       ├── labels.bin               # Binary labels
│       ├── pd/value.bin             # Binary PD values
│       ├── t1/value.bin             # Binary T1 values
│       ├── t2/value.bin             # Binary T2 values
│       └── t2s/value.bin            # Binary T2* values
└── warped_images/                   # Visualization outputs
```

## Customization

To modify the processing:

1. **Edit Configuration**: Modify `config.py`
   - Change subject ID, time frames, paths
   - Adjust processing flags and parameters

2. **Processing Options**: 
   - Set `runWarper=False` to skip warping
   - Set `runTexturizer=False` to skip texturization  
   - Set `runConverter=False` to skip binary conversion
   - Adjust `n_jobs` for parallel processing

3. **Paths**: Update paths in `config.py` for different subjects or data locations

## Performance Features

- **Smart Caching**: Automatically skips already processed slices
- **Parallel Processing**: Uses 8 CPU cores by default
- **Progress Tracking**: Real-time updates and comprehensive statistics
- **Error Recovery**: Automatic retry for failed slices
- **Validation**: Checks file completeness at each stage

## Troubleshooting

If you encounter issues:

1. **Run the test**: `./run_processing.sh test`
2. **Check status**: `./run_processing.sh status`
3. **Verify input files exist** in the expected locations
4. **Check disk space** for output directories
5. **Review error messages** for specific issues

## Migration from Notebook

This modular version provides the same functionality as `process_next_timeframe.ipynb` but with:
- ✅ Better organization and maintainability
- ✅ Improved error handling and recovery
- ✅ Command-line interface for automation
- ✅ Comprehensive progress tracking
- ✅ Smart caching to avoid reprocessing
- ✅ Parallel processing optimization

The core algorithms remain unchanged from the original implementation.

## Ready to Process

The pipeline is fully set up and ready to process your XCAT data. Start with:

```bash
./run_processing.sh all
```

This will process all 5 time frames with full warping, texturization, and binary conversion.
