#!/usr/bin/env python3
"""
XCAT Multi-Timeframe Processing Pipeline

A modular Python pipeline for processing XCAT cardiac VTI files across multiple time frames.
Refactored from the original Jupyter notebook for better organization and maintainability.

Modules:
- config: Configuration settings and constants
- data_loader: VTI file loading and preprocessing
- slice_processor: Core slice processing with parallel execution
- binary_converter: VTI to binary conversion functions
- utils: Utility functions and helpers
- main_processor: Main orchestration script

Usage:
    from process_multi_timeframe import main_processor
    main_processor.main()

Or run directly:
    python main_processor.py --all
"""

__version__ = "1.0.0"
__author__ = "XCAT Processing Team"
__description__ = "Multi-timeframe XCAT cardiac VTI processing pipeline"

# Import main components for easy access
from . import config
from . import data_loader
from . import slice_processor
from . import binary_converter
from . import utils
from . import main_processor

# Expose key functions
from .main_processor import process_time_frame, process_single_timeframe, main
from .config import get_paths_for_timeframe, create_directories, maskLabels
from .data_loader import load_and_preprocess_timeframe, validate_input_file
from .slice_processor import run_parallel_processing
from .binary_converter import run_binary_conversion, validate_binary_output

__all__ = [
    'config',
    'data_loader', 
    'slice_processor',
    'binary_converter',
    'utils',
    'main_processor',
    'process_time_frame',
    'process_single_timeframe',
    'main',
    'get_paths_for_timeframe',
    'create_directories',
    'maskLabels',
    'load_and_preprocess_timeframe',
    'validate_input_file',
    'run_parallel_processing',
    'run_binary_conversion',
    'validate_binary_output'
]
