#!/bin/bash

# XCAT Multi-Timeframe Processing Pipeline Launcher
# This script handles environment setup and launches the processing pipeline

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}XCAT Multi-Timeframe Processing Pipeline${NC}"
echo -e "${BLUE}========================================${NC}"

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# Function to check if conda is available
check_conda() {
    if command -v conda &> /dev/null; then
        echo -e "${GREEN}✅ Conda is available${NC}"
        return 0
    else
        echo -e "${RED}❌ Conda is not available${NC}"
        return 1
    fi
}

# Function to activate conda environment
activate_environment() {
    local env_name="$1"
    echo -e "${YELLOW}🔄 Attempting to activate conda environment: $env_name${NC}"
    
    # Source conda initialization
    if [ -f ~/.bashrc ]; then
        source ~/.bashrc
    fi
    
    # Try to activate the environment
    if conda activate "$env_name" 2>/dev/null; then
        echo -e "${GREEN}✅ Successfully activated environment: $env_name${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to activate environment: $env_name${NC}"
        return 1
    fi
}

# Function to check Python dependencies
check_dependencies() {
    echo -e "${YELLOW}🔍 Checking Python dependencies...${NC}"
    
    local missing_deps=()
    
    # Check each required dependency
    if ! python -c "import numpy" 2>/dev/null; then
        missing_deps+=("numpy")
    fi
    
    if ! python -c "import scipy" 2>/dev/null; then
        missing_deps+=("scipy")
    fi
    
    if ! python -c "import vtk" 2>/dev/null; then
        missing_deps+=("vtk")
    fi
    
    if ! python -c "import joblib" 2>/dev/null; then
        missing_deps+=("joblib")
    fi
    
    if [ ${#missing_deps[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ All required dependencies are available${NC}"
        return 0
    else
        echo -e "${RED}❌ Missing dependencies: ${missing_deps[*]}${NC}"
        echo -e "${YELLOW}💡 Try installing with: pip install ${missing_deps[*]}${NC}"
        return 1
    fi
}

# Function to run the processing pipeline
run_pipeline() {
    echo -e "${BLUE}🚀 Starting XCAT processing pipeline...${NC}"
    
    # Parse command line arguments
    if [ $# -eq 0 ]; then
        # No arguments, show help
        python main_processor.py --help
    elif [ "$1" = "test" ]; then
        # Run setup test
        echo -e "${YELLOW}🧪 Running setup test...${NC}"
        python test_setup.py
    elif [ "$1" = "status" ]; then
        # Show status
        python main_processor.py --status
    elif [ "$1" = "all" ]; then
        # Process all timeframes
        python main_processor.py --all
    elif [ "$1" = "frame" ] && [ -n "$2" ]; then
        # Process specific frame
        python main_processor.py --frame "$2"
    else
        # Pass all arguments to main processor
        python main_processor.py "$@"
    fi
}

# Main execution
main() {
    echo -e "${YELLOW}📋 Environment Setup${NC}"
    
    # Try to use conda if available
    if check_conda; then
        # Try different environment names
        for env_name in "xcat" "base"; do
            if activate_environment "$env_name"; then
                break
            fi
        done
    else
        echo -e "${YELLOW}⚠️ Using system Python${NC}"
    fi
    
    # Check dependencies
    if check_dependencies; then
        echo -e "${GREEN}✅ Environment ready${NC}"
    else
        echo -e "${RED}❌ Environment setup failed${NC}"
        echo -e "${YELLOW}💡 Please install missing dependencies and try again${NC}"
        exit 1
    fi
    
    # Run the pipeline
    run_pipeline "$@"
}

# Help function
show_help() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  test                    Run setup test"
    echo "  status                  Show processing status"
    echo "  all                     Process all time frames"
    echo "  frame N                 Process specific time frame N"
    echo "  help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 test                 # Test the setup"
    echo "  $0 status               # Check processing status"
    echo "  $0 all                  # Process all time frames"
    echo "  $0 frame 1              # Process time frame 1"
    echo ""
}

# Handle help
if [ "$1" = "help" ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Run main function
main "$@"
