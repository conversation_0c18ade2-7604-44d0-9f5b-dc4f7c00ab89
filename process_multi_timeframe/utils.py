#!/usr/bin/env python3
"""
Utility functions for XCAT processing pipeline
Contains helper functions for VTK operations, file checking, and module imports
"""

import os
import sys
import numpy as np
import vtk
from vtk.util.numpy_support import vtk_to_numpy, numpy_to_vtk

# Workaround for NumPy/VTK compatibility issue
if not hasattr(np, 'bool'):
    np.bool = np.bool_

def setup_module_paths():
    """Setup Python paths for importing local modules"""
    from config import MODULE_PATHS
    
    for path in MODULE_PATHS:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # Set PYTHONPATH environment variable
    os.environ['PYTHONPATH'] = ':'.join(MODULE_PATHS)

def import_modules_safely():
    """Safely import required modules with fallback mechanisms"""
    try:
        import myMorphingFunctions as MMF
        from texturizationFunctions import defineTissueProperties, fixLVTexture
        return MMF, defineTissueProperties, fixLVTexture
    except ImportError as e:
        print(f"First import attempt failed: {e}")
        try:
            # Manual import using importlib
            import importlib.util
            
            # Import myMorphingFunctions
            mmf_path = '/home/<USER>/XCAT_Project/myWarpFunctions/myMorphingFunctions.py'
            spec = importlib.util.spec_from_file_location("myMorphingFunctions", mmf_path)
            MMF = importlib.util.module_from_spec(spec)
            sys.modules["myMorphingFunctions"] = MMF
            spec.loader.exec_module(MMF)
            
            # Import texturizationFunctions
            tex_path = '/home/<USER>/XCAT_Project/myTexturizer/texturizationFunctions.py'
            spec = importlib.util.spec_from_file_location("texturizationFunctions", tex_path)
            tex_module = importlib.util.module_from_spec(spec)
            sys.modules["texturizationFunctions"] = tex_module
            spec.loader.exec_module(tex_module)
            
            defineTissueProperties = tex_module.defineTissueProperties
            fixLVTexture = tex_module.fixLVTexture
            
            return MMF, defineTissueProperties, fixLVTexture
        except Exception as e2:
            raise ImportError(f"Cannot import required modules: {e2}")

def parentVTI(img_slice, num_slices, size):
    """Create a parent VTI object containing the given slice."""
    parent_img = vtk.vtkImageData()
    dims = img_slice.GetDimensions()
    spacing = img_slice.GetSpacing()
    origin = img_slice.GetOrigin()
    
    parent_img.SetDimensions(dims[0], dims[1], num_slices)
    parent_img.SetSpacing(spacing)
    parent_img.SetOrigin(origin)
    
    pd = img_slice.GetPointData()
    for i in range(pd.GetNumberOfArrays()):
        array_name = pd.GetArrayName(i)
        array_data = vtk_to_numpy(pd.GetArray(i))
        
        if len(array_data.shape) == 1:
            extended_array = np.tile(array_data, num_slices)
        else:
            extended_array = np.tile(array_data, (num_slices, 1))
            
        vtk_array = numpy_to_vtk(extended_array)
        vtk_array.SetName(array_name)
        parent_img.GetPointData().AddArray(vtk_array)
    
    return parent_img

def voxelNumpyToVTK(BP_fill, mapx, mapy):
    """Create a VTK image data object from a NumPy array and mapping coordinates."""
    img = vtk.vtkImageData()
    img.SetDimensions(BP_fill.shape[0], BP_fill.shape[1], 1)
    img.SetSpacing(1.0, 1.0, 1.0)
    img.SetOrigin(0.0, 0.0, 0.0)
    
    BP_fill_flat = BP_fill.reshape(-1, order='F')
    BP_vtk = numpy_to_vtk(BP_fill_flat)
    BP_vtk.SetName('labels')
    img.GetPointData().AddArray(BP_vtk)
    
    if mapx is not None and mapy is not None:
        mapx_flat = mapx.reshape(-1, order='F')
        mapx_vtk = numpy_to_vtk(mapx_flat)
        mapx_vtk.SetName('mapx')
        img.GetPointData().AddArray(mapx_vtk)
        
        mapy_flat = mapy.reshape(-1, order='F')
        mapy_vtk = numpy_to_vtk(mapy_flat)
        mapy_vtk.SetName('mapy')
        img.GetPointData().AddArray(mapy_vtk)
    
    return img

def check_slice_completion(slice_idx, results_folder, runWarper, runTexturizer):
    """Check if specified slice is already processed."""
    slice_results_folder = f"{results_folder}/slice_{slice_idx:03d}"
    slice_tissue_folder = f"{results_folder}/Tissue_properties_initial_frame/slice_{slice_idx:03d}"
    
    required_files = []
    
    if runWarper:
        model_file = os.path.join(slice_results_folder, "Model.vti")
        required_files.append(model_file)
        
    if runTexturizer:
        tissue_files = [
            os.path.join(slice_tissue_folder, "PD.vti"),
            os.path.join(slice_tissue_folder, "T1.vti"), 
            os.path.join(slice_tissue_folder, "T2.vti"),
            os.path.join(slice_tissue_folder, "T2s.vti")
        ]
        required_files.extend(tissue_files)
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            return False
        if os.path.getsize(file_path) == 0:
            return False
            
    return True

def check_binary_conversion_completion(slice_output_folder_bin, runTexturizer=True):
    """Check if binary conversion is complete for a slice."""
    if not os.path.exists(slice_output_folder_bin):
        return False
    
    required_files = []
    
    labels_file = os.path.join(slice_output_folder_bin, "labels.bin")
    required_files.append(labels_file)
    
    if runTexturizer:
        tissue_properties = ["pd", "t1", "t2", "t2s"]
        for prop in tissue_properties:
            prop_file = os.path.join(slice_output_folder_bin, prop, "value.bin")
            required_files.append(prop_file)
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            return False
        if os.path.getsize(file_path) == 0:
            return False
            
    return True

def filter_unprocessed_slices(z_indices, results_folder, runWarper, runTexturizer):
    """Filter out slices that still need processing."""
    unprocessed = []
    completed = []
    
    for slice_idx in z_indices:
        if check_slice_completion(slice_idx, results_folder, runWarper, runTexturizer):
            completed.append(slice_idx)
        else:
            unprocessed.append(slice_idx)
    
    return unprocessed, completed

def filter_unconverted_slices(z_indices, output_folder_bin, runTexturizer):
    """Filter out slices that still need binary conversion."""
    unconverted = []
    converted = []
    
    for slice_idx in z_indices:
        slice_output_folder_bin = f"{output_folder_bin}/slice_{slice_idx:03d}"
        if check_binary_conversion_completion(slice_output_folder_bin, runTexturizer):
            converted.append(slice_idx)
        else:
            unconverted.append(slice_idx)
    
    return unconverted, converted

def setup_matplotlib_backend():
    """Setup matplotlib for non-interactive use"""
    import matplotlib
    matplotlib.use('Agg')
    import matplotlib.pyplot as plt
    return plt

def print_processing_statistics(total_time, total_slices, processed_slices, 
                              successful_processed, already_completed):
    """Print comprehensive processing statistics"""
    total_successful = successful_processed + already_completed
    
    print(f"\n🎉 === Processing Complete ===")
    print(f"📊 Statistics:")
    print(f"   Total time: {total_time/60:.1f} minutes")
    print(f"   Total slices: {total_slices}")
    print(f"   Already completed: {already_completed} (skipped)")
    print(f"   Newly processed: {processed_slices}")
    print(f"   Newly successful: {successful_processed}/{processed_slices}")
    print(f"   Total successful: {total_successful}/{total_slices}")
    print(f"   Total success rate: {total_successful/total_slices*100:.1f}%")
    if processed_slices > 0:
        print(f"   Average speed: {total_time/processed_slices:.1f} seconds/slice")

def add_functions_to_mmf(MMF):
    """Add utility functions to MMF module"""
    MMF.parentVTI = parentVTI
    MMF.voxelNumpyToVTK = voxelNumpyToVTK
    return MMF
