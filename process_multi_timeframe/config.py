#!/usr/bin/env python3
"""
Configuration file for XCAT processing pipeline
Contains all configuration settings, paths, and constants
"""

import os
import numpy as np

# ============================================================================
# Subject Configuration
# ============================================================================
SUBJECT_ID = "female_151"
SUBJECT_TYPE = "female"
SUBJECT_NUMBER = "151"
TIME_FRAMES = [1, 2, 3, 4, 5]  # List of time frames to process
SUBFOLDER = 'cine'

# ============================================================================
# Base Paths
# ============================================================================
BASE_DATA_PATH = "/home/<USER>/XCAT_Project/output"
BASE_OUTPUT_PATH = "/home/<USER>/XCAT_Project/outputData"

# ============================================================================
# Processing Flags
# ============================================================================
runWarper = True
runTexturizer = True
runConverter = True
process_all_slices = True
n_jobs = 8

# ============================================================================
# External Dependencies Paths
# ============================================================================
# Network path for texturizer
network_path = '/home/<USER>/XCAT_Project/myTexturizer/LAXsegnet'

# B0 field path
b0_map_path = '/home/<USER>/XCAT_Project/output/male_169/male_169_field_Hz_20250610_211706.npy'

# Module paths
MODULE_PATHS = [
    '/home/<USER>/XCAT_Project',
    '/home/<USER>/XCAT_Project/myWarpFunctions',
    '/home/<USER>/XCAT_Project/myTexturizer'
]

# ============================================================================
# Processing Parameters
# ============================================================================
# Binary conversion target size
BINARY_TARGET_SIZE = [500, 500, 1]

# Slice extension parameters
SLICE_EXTENSION_BEFORE = 50
SLICE_EXTENSION_AFTER = 50

# ============================================================================
# Label Definitions
# ============================================================================
class myLabels:
    """Class to define anatomical labels for XCAT phantom"""
    def __init__(self, LV_wall, RV_wall, LA_wall, RA_wall, LV_blood, RV_blood, LA_blood, RA_blood,
                 body, muscle, brain, sinus, liver, gall_bladder, right_lung, left_lung,
                 esophagus, esophagus_contents, laryngopharynx, stomach_wall, stomach_contents,
                 pancreas, right_kidney_cortex, right_kidney_medulla, left_kidney_cortex,
                 left_kidney_medulla, adrenal, right_renal_pelvis, left_renal_pelvis,
                 spleen, ribs, cortical_bone, spine, spinal_cord, bone_marrow, arteries,
                 veins, bladder, prostate, ascending_intestine, transverse_intestine,
                 descending_intestine, small_intestine, rectum, seminal_vesicles,
                 vas_deferens, testes, epididymis, ejaculatory_duct, pericardium,
                 cartilage, intestine_air, ureter, urethra, lymph, lymph_abnormal,
                 trachea_bronchi, airways, thyroid, thymus):
        self.LV_wall = LV_wall
        self.RV_wall = RV_wall
        self.LA_wall = LA_wall
        self.RA_wall = RA_wall
        self.LV_blood = LV_blood
        self.RV_blood = RV_blood
        self.LA_blood = LA_blood
        self.RA_blood = RA_blood
        self.body = body
        self.muscle = muscle
        self.brain = brain
        self.sinus = sinus
        self.liver = liver
        self.gall_bladder = gall_bladder
        self.right_lung = right_lung
        self.left_lung = left_lung
        self.esophagus = esophagus
        self.esophagus_contents = esophagus_contents
        self.laryngopharynx = laryngopharynx
        self.stomach_wall = stomach_wall
        self.stomach_contents = stomach_contents
        self.pancreas = pancreas
        self.right_kidney_cortex = right_kidney_cortex
        self.right_kidney_medulla = right_kidney_medulla
        self.left_kidney_cortex = left_kidney_cortex
        self.left_kidney_medulla = left_kidney_medulla
        self.adrenal = adrenal
        self.right_renal_pelvis = right_renal_pelvis
        self.left_renal_pelvis = left_renal_pelvis
        self.spleen = spleen
        self.ribs = ribs
        self.cortical_bone = cortical_bone
        self.spine = spine
        self.spinal_cord = spinal_cord
        self.bone_marrow = bone_marrow
        self.arteries = arteries
        self.veins = veins
        self.bladder = bladder
        self.prostate = prostate
        self.ascending_intestine = ascending_intestine
        self.transverse_intestine = transverse_intestine
        self.descending_intestine = descending_intestine
        self.small_intestine = small_intestine
        self.rectum = rectum
        self.seminal_vesicles = seminal_vesicles
        self.vas_deferens = vas_deferens
        self.testes = testes
        self.epididymis = epididymis
        self.ejaculatory_duct = ejaculatory_duct
        self.Peri = pericardium
        self.cartilage = cartilage
        self.intestine_air = intestine_air
        self.ureter = ureter
        self.urethra = urethra
        self.lymph = lymph
        self.lymph_abnormal = lymph_abnormal
        self.trachea_bronchi = trachea_bronchi
        self.airways = airways
        self.thyroid = thyroid
        self.thymus = thymus

# Initialize mask labels with standard XCAT values
maskLabels = myLabels(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 
                     17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 
                     31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 
                     45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 
                     65, 66)

# ============================================================================
# Path Generation Functions
# ============================================================================
def get_paths_for_timeframe(time_frame):
    """Generate all necessary paths for a given time frame"""
    data_folder = BASE_DATA_PATH
    subject_folder = f"{data_folder}/{SUBJECT_ID}/{SUBFOLDER}"
    results_folder = f"{BASE_OUTPUT_PATH}/{SUBJECT_ID}/{SUBFOLDER}/frame_{time_frame:02d}"
    output_folder_bin = f"{BASE_OUTPUT_PATH}/{SUBJECT_ID}/{SUBFOLDER}/frame_{time_frame:02d}/bin_output"
    warped_images = f"{BASE_OUTPUT_PATH}/{SUBJECT_ID}/{SUBFOLDER}/frame_{time_frame:02d}/warped_images"
    file_input = f"{subject_folder}/{SUBJECT_ID}_cine.samp_act_{time_frame}.vti"
    
    return {
        'data_folder': data_folder,
        'subject_folder': subject_folder,
        'results_folder': results_folder,
        'output_folder_bin': output_folder_bin,
        'warped_images': warped_images,
        'file_input': file_input
    }

def create_directories(paths):
    """Create all necessary directories for processing"""
    directories_to_create = [
        paths['results_folder'],
        paths['output_folder_bin'],
        paths['warped_images'],
        f"{paths['results_folder']}/SAX_slices",
        f"{paths['results_folder']}/Tissue_properties_initial_frame"
    ]
    
    for directory in directories_to_create:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
