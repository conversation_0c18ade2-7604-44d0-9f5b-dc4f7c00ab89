# XCAT Multi-Timeframe Processing Pipeline

This directory contains a modular Python pipeline for processing XCAT cardiac VTI files across multiple time frames. The pipeline has been refactored from the original Jupyter notebook to provide better organization, reusability, and maintainability.

## Overview

The pipeline processes cardiac VTI files through three main stages:
1. **Warping**: Slice-by-slice processing with morphing functions
2. **Texturization**: Tissue property generation using neural networks
3. **Binary Conversion**: Converting VTI files to binary format for downstream processing

## File Structure

```
process_multi_timeframe/
├── config.py              # Configuration settings and constants
├── data_loader.py          # VTI file loading and preprocessing
├── slice_processor.py      # Core slice processing with parallel execution
├── binary_converter.py     # VTI to binary conversion functions
├── utils.py               # Utility functions and helpers
├── main_processor.py       # Main orchestration script
└── README.md              # This file
```

## Module Descriptions

### config.py
- Subject configuration (ID, type, time frames)
- Path definitions and generation functions
- Processing flags and parameters
- Anatomical label definitions
- Module path setup

### data_loader.py
- VTI file loading and validation
- Label extraction and preprocessing
- Tissue mask creation
- Cardiac slice identification
- Data structure preparation

### slice_processor.py
- Individual slice processing
- Parallel execution management
- Progress tracking and error handling
- Retry mechanisms for failed slices

### binary_converter.py
- VTI to binary format conversion
- File validation and completion checking
- Batch processing with progress tracking
- Output validation

### utils.py
- Module import management
- VTK utility functions
- File checking and validation
- Statistics and reporting functions

### main_processor.py
- Complete workflow orchestration
- Command-line interface
- Status reporting
- Error handling and logging

## Usage

### Basic Usage

Process all time frames:
```bash
cd /home/<USER>/XCAT_Project/process_multi_timeframe
python main_processor.py --all
```

Process a single time frame:
```bash
python main_processor.py --frame 1
```

Check processing status:
```bash
python main_processor.py --status
```

### Configuration

Edit `config.py` to modify:
- Subject ID and type
- Time frames to process
- Input/output paths
- Processing parameters
- Number of parallel jobs

### Key Configuration Variables

```python
SUBJECT_ID = "female_151"           # Subject identifier
TIME_FRAMES = [1, 2, 3, 4, 5]      # Time frames to process
runWarper = True                    # Enable warping
runTexturizer = True                # Enable texturization
runConverter = True                 # Enable binary conversion
n_jobs = 8                          # Parallel processing jobs
```

## Features

### Parallel Processing
- Automatic detection of available CPU cores
- Intelligent load balancing
- Progress tracking with joblib
- Automatic retry for failed slices

### File Management
- Automatic directory creation
- Completion checking to avoid reprocessing
- Comprehensive error handling
- Progress persistence

### Error Handling
- Graceful failure recovery
- Detailed error logging
- Retry mechanisms
- Validation at each stage

### Progress Tracking
- Real-time progress updates
- Comprehensive statistics
- Time estimation
- Success/failure reporting

## Dependencies

The pipeline requires the same dependencies as the original notebook:
- Python 3.8+
- VTK
- NumPy
- SciPy
- joblib
- matplotlib (for visualization)
- Custom modules:
  - myMorphingFunctions (from myWarpFunctions/)
  - texturizationFunctions (from myTexturizer/)

## Output Structure

For each time frame, the pipeline creates:
```
outputData/{SUBJECT_ID}/cine/frame_{XX}/
├── slice_XXX/
│   └── Model.vti                    # Warped slice data
├── Tissue_properties_initial_frame/
│   └── slice_XXX/
│       ├── PD.vti                   # Proton density
│       ├── T1.vti                   # T1 relaxation
│       ├── T2.vti                   # T2 relaxation
│       └── T2s.vti                  # T2* relaxation
├── bin_output/
│   └── slice_XXX/
│       ├── labels.bin               # Binary labels
│       ├── pd/value.bin             # Binary PD values
│       ├── t1/value.bin             # Binary T1 values
│       ├── t2/value.bin             # Binary T2 values
│       └── t2s/value.bin            # Binary T2* values
└── warped_images/                   # Visualization outputs
```

## Performance Optimization

- **Parallel Processing**: Utilizes multiple CPU cores for slice processing
- **Smart Caching**: Skips already processed slices automatically
- **Memory Management**: Processes slices individually to manage memory usage
- **Progress Persistence**: Resumes from where it left off if interrupted

## Troubleshooting

### Common Issues

1. **Module Import Errors**
   - Ensure myWarpFunctions and myTexturizer directories are accessible
   - Check Python path configuration in config.py

2. **Memory Issues**
   - Reduce n_jobs in config.py
   - Process fewer time frames at once

3. **File Not Found Errors**
   - Verify input file paths in config.py
   - Check that VTI files exist in expected locations

4. **Permission Errors**
   - Ensure write permissions for output directories
   - Check disk space availability

### Debug Mode

For detailed debugging, modify the scripts to increase verbosity or add print statements as needed.

## Migration from Notebook

This modular version provides the same functionality as the original notebook but with:
- Better error handling and recovery
- Improved progress tracking
- Modular design for easier maintenance
- Command-line interface for automation
- Comprehensive logging and statistics

The core algorithms and processing logic remain unchanged from the original implementation.
