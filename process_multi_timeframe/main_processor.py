#!/usr/bin/env python3
"""
Main processing script for XCAT multi-timeframe processing pipeline
Orchestrates the complete processing workflow for multiple time frames
"""

import os
import sys
import time
import logging
import numpy as np

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import (TIME_FRAMES, runWarper, runTexturizer, runConverter, runCropper, n_jobs,
                   network_path, maskLabels, get_paths_for_timeframe, create_directories)
from data_loader import load_and_preprocess_timeframe, validate_input_file
from slice_processor import run_parallel_processing
from binary_converter import run_binary_conversion, validate_binary_output
from cardiac_cropper import crop_cardiac_region, crop_b0_field_aligned, save_cardiac_npz
from t2star_updater import process_t2star_volume_3d, load_t2star_volume_from_slices
from label_cleaner import apply_label_cleaning_to_volume
from shimming import apply_shimming_to_b0_field, process_shimmed_b0_for_cardiac_region
from logger import get_logger, init_logger, log_info, log_error, log_warning, log_exception
from utils import setup_module_paths, import_modules_safely, add_functions_to_mmf, parentVTI, voxelNumpyToVTK

def get_subject_id_from_config():
    """Get subject ID from config"""
    try:
        from config import SUBJECT_ID
        return SUBJECT_ID
    except ImportError:
        return "unknown"

def get_b0_map_path(time_frame):
    """Get B0 map path for the given timeframe"""
    try:
        from config import b0_map_path
        # Use the B0 map path from config
        return b0_map_path if os.path.exists(b0_map_path) else None
    except ImportError:
        return None

def create_cardiac_mask_from_labels(labels_volume):
    """
    Create cardiac mask from cleaned labels volume

    Parameters:
    -----------
    labels_volume : ndarray
        3D array with cleaned tissue labels

    Returns:
    --------
    ndarray
        Binary cardiac mask
    """
    # Cardiac tissue labels (after cleaning)
    cardiac_labels = [1, 2, 3, 4, 5, 6, 7, 8, 50]  # LV wall, RV wall, LA wall, RA wall, LV blood, RV blood, LA blood, RA blood, pericardium

    cardiac_mask = np.zeros_like(labels_volume, dtype=bool)
    for label in cardiac_labels:
        cardiac_mask |= (labels_volume == label)

    print(f"      Created cardiac mask with {np.sum(cardiac_mask)} voxels")
    return cardiac_mask

def create_b0_cardiac_mask(cardiac_mask, b0_shape):
    """
    Create cardiac mask for B0 field with matching dimensions

    Parameters:
    -----------
    cardiac_mask : ndarray
        Original cardiac mask
    b0_shape : tuple
        Shape of B0 field

    Returns:
    --------
    ndarray
        Cardiac mask resized to match B0 field
    """
    from scipy.ndimage import zoom

    if cardiac_mask.shape == b0_shape:
        return cardiac_mask

    # Calculate zoom factors
    zoom_factors = [b0_shape[i] / cardiac_mask.shape[i] for i in range(3)]

    # Resize cardiac mask to match B0 field
    b0_cardiac_mask = zoom(cardiac_mask.astype(float), zoom_factors, order=0) > 0.5

    print(f"      Resized cardiac mask from {cardiac_mask.shape} to {b0_cardiac_mask.shape}")
    return b0_cardiac_mask

def load_and_crop_b0_fields(time_frame, crop_bounds, cardiac_mask=None, z_indices=None):
    """Load and crop B0 fields if available"""
    b0_cardiac = None
    shimmed_b0_cardiac = None

    try:
        from config import b0_map_path

        # Load the B0 field from config
        if os.path.exists(b0_map_path):
            print(f"      Loading B0 field from: {b0_map_path}")
            b0_field = np.load(b0_map_path)
            print(f"      Original B0 field shape: {b0_field.shape}")

            # Use actual processed slice range instead of hardcoded values
            if z_indices is not None and len(z_indices) > 0:
                b0_start_slice = min(z_indices)
                b0_end_slice = max(z_indices)
                print(f"      Using processed slice range: {b0_start_slice} to {b0_end_slice}")
            else:
                # Fallback to hardcoded values if z_indices not available
                b0_start_slice = 199
                b0_end_slice = 306
                print(f"      Using fallback slice range: {b0_start_slice} to {b0_end_slice}")

            if b0_field.ndim == 3 and b0_field.shape[2] > b0_end_slice:
                b0_extracted = b0_field[:, :, b0_start_slice:b0_end_slice+1]
                # Flip along z-axis to match XCAT orientation
                b0_extracted = np.flip(b0_extracted, axis=2)
                print(f"      Extracted B0 region: {b0_extracted.shape}")

                # Apply shimming if cardiac mask is available
                if cardiac_mask is not None:
                    print(f"      Applying shimming to B0 field...")
                    logger = get_logger()
                    logger.info("Starting B0 shimming optimization...")

                    # Create cardiac mask for the extracted B0 region
                    b0_cardiac_mask = create_b0_cardiac_mask(cardiac_mask, b0_extracted.shape)
                    logger.info(f"Created B0 cardiac mask: {b0_cardiac_mask.shape}, {np.sum(b0_cardiac_mask)} voxels")

                    # Apply shimming with detailed logging
                    shimmed_b0_full = apply_shimming_to_b0_field(b0_extracted, b0_cardiac_mask)
                    logger.info("B0 shimming optimization completed")

                    # Process shimmed B0 for cardiac region
                    target_shape = b0_extracted.shape  # Use actual extracted shape
                    shimmed_b0_oriented = process_shimmed_b0_for_cardiac_region(
                        shimmed_b0_full, b0_start_slice, b0_end_slice, target_shape
                    )

                    # Crop both original and shimmed B0 to cardiac bounds
                    if crop_bounds:
                        b0_cardiac = crop_b0_field_aligned(b0_extracted, crop_bounds)
                        shimmed_b0_cardiac = crop_b0_field_aligned(shimmed_b0_oriented, crop_bounds)
                        print(f"      Cropped B0 field: {b0_cardiac.shape}")
                        print(f"      Cropped shimmed B0 field: {shimmed_b0_cardiac.shape}")
                    else:
                        b0_cardiac = b0_extracted
                        shimmed_b0_cardiac = shimmed_b0_oriented
                else:
                    # No cardiac mask available, just crop original B0
                    if crop_bounds:
                        b0_cardiac = crop_b0_field_aligned(b0_extracted, crop_bounds)
                        print(f"      Cropped B0 field: {b0_cardiac.shape}")
                    else:
                        b0_cardiac = b0_extracted
            else:
                print(f"      Warning: B0 field shape {b0_field.shape} doesn't contain slice range {b0_start_slice}-{b0_end_slice}")

    except Exception as e:
        print(f"      Warning: Could not load B0 fields: {str(e)}")
        import traceback
        traceback.print_exc()

    return b0_cardiac, shimmed_b0_cardiac

def apply_shimming_if_available(b0_cardiac, time_frame):
    """Apply shimming to B0 field if shimming functionality is available"""
    if b0_cardiac is None:
        return None

    try:
        # This is where shimming functionality from the notebook would go
        # For now, create a simple shimmed version (reduced field variation)
        shimmed_b0 = b0_cardiac * 0.5  # Simple shimming simulation
        print(f"      Applied basic shimming to B0 field")
        return shimmed_b0
    except Exception as e:
        print(f"      Warning: Could not apply shimming: {str(e)}")
        return None

def print_cardiac_summary(cardiac_params, b0_cardiac=None, shimmed_b0_cardiac=None):
    """Print summary of saved cardiac parameters"""
    print("\n   Summary of cardiac parameter maps:")
    print(f"   {'Parameter':<12} {'Shape':<15} {'Type':<12} {'Range':<20}")
    print("   " + "-" * 60)

    for name, data in cardiac_params.items():
        if name.startswith('_'):  # Skip metadata
            continue
        if data is not None:
            data_min, data_max = np.min(data), np.max(data)
            print(f"   {name:<12} {str(data.shape):<15} {str(data.dtype):<12} [{data_min:.2f}, {data_max:.2f}]")

    if b0_cardiac is not None:
        data_min, data_max = np.min(b0_cardiac), np.max(b0_cardiac)
        print(f"   {'B0':<12} {str(b0_cardiac.shape):<15} {str(b0_cardiac.dtype):<12} [{data_min:.2f}, {data_max:.2f}]")

    if shimmed_b0_cardiac is not None:
        data_min, data_max = np.min(shimmed_b0_cardiac), np.max(shimmed_b0_cardiac)
        print(f"   {'ShimmedB0':<12} {str(shimmed_b0_cardiac.shape):<15} {str(shimmed_b0_cardiac.dtype):<12} [{data_min:.2f}, {data_max:.2f}]")

def load_processed_parameters(results_folder, z_indices):
    """
    Load processed parameter maps from VTI files

    Args:
        results_folder: Path to results folder containing slice folders
        z_indices: List of slice indices that were processed

    Returns:
        dict: Dictionary containing loaded parameter maps
    """
    import vtk
    from vtk.util.numpy_support import vtk_to_numpy

    print(f"Loading processed parameters from {results_folder}")

    # Find a representative slice to get dimensions
    sample_slice = None
    for slice_idx in z_indices:
        slice_folder = f"{results_folder}/slice_{slice_idx:03d}"
        model_file = os.path.join(slice_folder, "Model.vti")
        if os.path.exists(model_file):
            sample_slice = slice_idx
            break

    if sample_slice is None:
        print("No processed slices found")
        return None

    # Load sample slice to get dimensions
    sample_folder = f"{results_folder}/slice_{sample_slice:03d}"
    sample_model = os.path.join(sample_folder, "Model.vti")

    reader = vtk.vtkXMLImageDataReader()
    reader.SetFileName(sample_model)
    reader.Update()
    sample_data = reader.GetOutput()

    # Get dimensions for full volume
    slice_dims = sample_data.GetDimensions()
    full_dims = (slice_dims[0], slice_dims[1], len(z_indices))

    print(f"Reconstructing volume with dimensions: {full_dims}")

    # Initialize parameter arrays
    params = {
        'Labels': np.zeros(full_dims, dtype=np.uint8),
        'PD': np.zeros(full_dims, dtype=np.float32),
        'T1': np.zeros(full_dims, dtype=np.float32),
        'T2': np.zeros(full_dims, dtype=np.float32),
        'T2s': np.zeros(full_dims, dtype=np.float32),
        'T2*': np.zeros(full_dims, dtype=np.float32)  # Add T2* parameter
    }

    # Load each slice
    for i, slice_idx in enumerate(z_indices):
        slice_folder = f"{results_folder}/slice_{slice_idx:03d}"
        tissue_folder = f"{results_folder}/Tissue_properties_initial_frame/slice_{slice_idx:03d}"

        # Load labels from Model.vti
        model_file = os.path.join(slice_folder, "Model.vti")
        if os.path.exists(model_file):
            reader.SetFileName(model_file)
            reader.Update()
            model_data = reader.GetOutput()

            if model_data.GetPointData().HasArray('labels'):
                labels_array = vtk_to_numpy(model_data.GetPointData().GetArray('labels'))
                labels_2d = labels_array.reshape(slice_dims[0], slice_dims[1], order='F')
                params['Labels'][:, :, i] = labels_2d

        # Load tissue properties
        for prop_name, file_name in [('PD', 'PD.vti'), ('T1', 'T1.vti'),
                                   ('T2', 'T2.vti'), ('T2s', 'T2s.vti'), ('T2*', 'T2s.vti')]:
            prop_file = os.path.join(tissue_folder, file_name)
            if os.path.exists(prop_file):
                reader.SetFileName(prop_file)
                reader.Update()
                prop_data = reader.GetOutput()

                # Try different array names
                array_name = None
                if prop_data.GetPointData().HasArray('parameters'):
                    array_name = 'parameters'
                elif prop_data.GetPointData().HasArray(prop_name):
                    array_name = prop_name
                elif prop_data.GetPointData().HasArray('T2s') and prop_name == 'T2*':
                    array_name = 'T2s'  # Use T2s data for T2* initially

                if array_name:
                    prop_array = vtk_to_numpy(prop_data.GetPointData().GetArray(array_name))
                    prop_2d = prop_array.reshape(slice_dims[0], slice_dims[1], order='F')
                    params[prop_name][:, :, i] = prop_2d

    print(f"Successfully loaded parameters for {len(z_indices)} slices")
    return params

def process_time_frame(time_frame):
    """Process a single time frame completely"""
    print(f"\n{'='*60}")
    print(f"PROCESSING TIME FRAME {time_frame}")
    print(f"{'='*60}")
    
    # Generate paths for this time frame
    paths = get_paths_for_timeframe(time_frame)
    
    print(f"Input file: {paths['file_input']}")
    print(f"Output folder: {paths['results_folder']}")
    
    # Validate input file
    if not validate_input_file(paths['file_input']):
        return False
    
    # Create directories
    create_directories(paths)
    
    # Load and preprocess data
    try:
        data = load_and_preprocess_timeframe(paths['file_input'], maskLabels)
    except Exception as e:
        print(f"❌ Error loading data: {str(e)}")
        return False
    
    if len(data['z_indices']) == 0:
        print("❌ No slices to process!")
        return False
    
    print(f"Processing z-range: {data['z_indices'][0]} to {data['z_indices'][-1]} ({len(data['z_indices'])} slices)")
    
    # Process slices
    if runWarper or runTexturizer:
        print(f"\n🔄 Starting slice processing...")
        results = run_parallel_processing(
            data['data_input'], data['labels'], data['Target_mask'], maskLabels, 
            data['spacing'], data['origin'], data['dim'],
            paths['results_folder'], paths['warped_images'], runWarper, runTexturizer, network_path,
            data['z_indices'], voxelNumpyToVTK, parentVTI, 
            n_jobs=n_jobs,
            check_existing=True,
            force_reprocess=False
        )
        
        if not results or sum(results) == 0:
            print("❌ Slice processing failed!")
            return False
        
        successful_slices = sum(results)
        print(f"✅ Slice processing completed: {successful_slices}/{len(data['z_indices'])} successful")
    
    # Convert to binary format
    if runConverter:
        print(f"\n🔄 Starting binary conversion...")
        successful_conversions, failed_conversions = run_binary_conversion(
            data['z_indices'], paths['results_folder'], paths['output_folder_bin'],
            runTexturizer, force_reconvert=False
        )

        if failed_conversions > 0:
            print(f"⚠️ Some binary conversions failed: {failed_conversions}")

        # Validate binary output
        validate_binary_output(paths['output_folder_bin'], data['z_indices'], runTexturizer)

    # Cardiac cropping and NPZ saving
    if runCropper:
        logger = get_logger()
        logger.log_stage_start("Cardiac Processing (T2* update, label cleaning, cropping, NPZ saving)")

        try:
            # Load processed parameter maps from VTI files
            logger.info("Loading processed parameter maps from VTI files...")
            params = load_processed_parameters(paths['results_folder'], data['z_indices'])

            if params:
                logger.log_parameter_summary({
                    'Loaded parameters': list(params.keys()),
                    'Volume shape': params['Labels'].shape if 'Labels' in params else 'Unknown'
                })

                # Step 1: Apply label cleaning to remove wrong LV/RV labels
                logger.info("Step 1: Cleaning LV/RV labels...")
                params = apply_label_cleaning_to_volume(params, body_label=9, debug=False)
                logger.info("Label cleaning completed")

                # Step 2: Create cardiac mask AFTER label cleaning
                logger.info("Step 2: Creating cardiac mask from cleaned labels...")
                cleaned_cardiac_mask = create_cardiac_mask_from_labels(params['Labels'])
                logger.info(f"Created cardiac mask with {np.sum(cleaned_cardiac_mask)} voxels")

                # Step 3: T2* update with B0 field effects (if B0 map available)
                logger.info("Step 3: Updating T2* with B0 field effects...")
                b0_map_path = get_b0_map_path(time_frame)
                if b0_map_path and os.path.exists(b0_map_path):
                    logger.info(f"Found B0 map: {b0_map_path}")
                    try:
                        t2star_plus = process_t2star_volume_3d(
                            params['T2s'], params['T2'], b0_map_path,
                            output_folder=paths['results_folder'],
                            z_indices=data['z_indices'],
                            labels_volume=params['Labels']
                        )
                        params['T2*+'] = t2star_plus
                        logger.info("T2* update completed successfully")
                    except Exception as t2_error:
                        logger.error(f"T2* update failed: {str(t2_error)}")
                        logger.log_exception("T2* update exception details")
                        # Use original T2* as fallback
                        params['T2*+'] = params['T2s']
                        logger.warning("Using original T2* values as fallback")
                else:
                    logger.warning("No B0 map found, using original T2* values")
                    params['T2*+'] = params['T2s']

                # Step 4: Crop cardiac region using CLEANED cardiac mask
                logger.info("Step 4: Cropping cardiac region using cleaned labels...")
                cardiac_params = crop_cardiac_region(params, cleaned_cardiac_mask,
                                                   crop_size=(150, 150, 150), debug=False)
                logger.info(f"Cardiac cropping completed: {cardiac_params['Labels'].shape}")

                # Step 5: Load and crop B0 fields if available
                logger.info("Step 5: Processing B0 fields...")
                b0_cardiac, shimmed_b0_cardiac = load_and_crop_b0_fields(
                    time_frame, cardiac_params.get('_bounds'), cleaned_cardiac_mask, data['z_indices']
                )

                if b0_cardiac is not None:
                    logger.info(f"B0 field processed: {b0_cardiac.shape}")
                if shimmed_b0_cardiac is not None:
                    logger.info(f"Shimmed B0 field processed: {shimmed_b0_cardiac.shape}")

                # Step 6: Save cardiac NPZ file
                logger.info("Step 6: Saving cardiac NPZ file...")
                npz_path, metadata_path = save_cardiac_npz(
                    cardiac_params, b0_cardiac, shimmed_b0_cardiac,
                    paths['output_folder_bin'], time_frame,
                    get_subject_id_from_config()
                )

                logger.log_file_operation("NPZ file saved", npz_path, True)
                logger.log_file_operation("Metadata saved", metadata_path, True)

                # Print summary of what was saved
                print_cardiac_summary(cardiac_params, b0_cardiac, shimmed_b0_cardiac)

                logger.log_stage_end("Cardiac Processing", True)

            else:
                logger.error("Could not load processed parameters for cardiac processing")
                logger.log_stage_end("Cardiac Processing", False)

        except Exception as e:
            logger.error(f"Cardiac processing failed: {str(e)}")
            logger.log_exception("Cardiac processing exception details")
            logger.log_stage_end("Cardiac Processing", False)

    print(f"\n✅ Time frame {time_frame} processing complete!")
    return True

def main():
    """Main function to process all time frames"""
    # Initialize logging
    logger = init_logger(log_dir="logs", log_level=logging.INFO)

    logger.info("Starting multi-timeframe XCAT processing")
    logger.info(f"Time frames to process: {TIME_FRAMES}")
    logger.info(f"Processing flags: Warper={runWarper}, Texturizer={runTexturizer}, Converter={runConverter}, Cropper={runCropper}")
    logger.info(f"Parallel jobs: {n_jobs}")

    # Setup module paths
    setup_module_paths()
    
    # Test module imports
    try:
        MMF, defineTissueProperties, fixLVTexture = import_modules_safely()
        MMF = add_functions_to_mmf(MMF)
        print("✅ Module imports successful")
    except Exception as e:
        print(f"❌ Module import failed: {e}")
        return False
    
    successful_frames = 0
    failed_frames = 0
    start_time = time.time()
    
    for time_frame in TIME_FRAMES:
        try:
            print(f"\n{'='*80}")
            print(f"STARTING TIME FRAME {time_frame}")
            print(f"{'='*80}")
            
            result = process_time_frame(time_frame)
            if result:
                successful_frames += 1
                print(f"✅ Time frame {time_frame} completed successfully")
            else:
                failed_frames += 1
                print(f"❌ Time frame {time_frame} failed")
        except Exception as e:
            print(f"❌ Error processing time frame {time_frame}: {str(e)}")
            import traceback
            traceback.print_exc()
            failed_frames += 1
    
    total_time = time.time() - start_time
    
    print(f"\n{'='*80}")
    print(f"MULTI-TIMEFRAME PROCESSING COMPLETE")
    print(f"{'='*80}")
    print(f"Total time: {total_time/60:.1f} minutes")
    print(f"Successful frames: {successful_frames}/{len(TIME_FRAMES)}")
    print(f"Failed frames: {failed_frames}/{len(TIME_FRAMES)}")
    print(f"Success rate: {successful_frames/len(TIME_FRAMES)*100:.1f}%")
    
    if successful_frames == len(TIME_FRAMES):
        print("🎉 All time frames processed successfully!")
        return True
    else:
        print(f"⚠️ {failed_frames} time frames failed processing")
        return False

def process_single_timeframe(time_frame):
    """Process a single specific time frame (utility function)"""
    print(f"Processing single time frame: {time_frame}")
    
    # Setup module paths
    setup_module_paths()
    
    # Test module imports
    try:
        MMF, defineTissueProperties, fixLVTexture = import_modules_safely()
        MMF = add_functions_to_mmf(MMF)
        print("✅ Module imports successful")
    except Exception as e:
        print(f"❌ Module import failed: {e}")
        return False
    
    return process_time_frame(time_frame)

def get_processing_status():
    """Get status of processing for all time frames"""
    status = {}
    
    for time_frame in TIME_FRAMES:
        paths = get_paths_for_timeframe(time_frame)
        
        # Check if input file exists
        input_exists = os.path.exists(paths['file_input'])
        
        # Check if results folder exists
        results_exist = os.path.exists(paths['results_folder'])
        
        # Check if binary output exists
        binary_exists = os.path.exists(paths['output_folder_bin'])
        
        # Count processed slices if results exist
        processed_slices = 0
        if results_exist:
            slice_folders = [f for f in os.listdir(paths['results_folder']) 
                           if f.startswith('slice_') and os.path.isdir(os.path.join(paths['results_folder'], f))]
            processed_slices = len(slice_folders)
        
        status[time_frame] = {
            'input_exists': input_exists,
            'results_exist': results_exist,
            'binary_exists': binary_exists,
            'processed_slices': processed_slices,
            'paths': paths
        }
    
    return status

def print_status_report():
    """Print a comprehensive status report"""
    print("\n📊 PROCESSING STATUS REPORT")
    print("="*60)
    
    status = get_processing_status()
    
    for time_frame, info in status.items():
        print(f"\nTime Frame {time_frame}:")
        print(f"  Input file: {'✅' if info['input_exists'] else '❌'}")
        print(f"  Results: {'✅' if info['results_exist'] else '❌'}")
        print(f"  Binary output: {'✅' if info['binary_exists'] else '❌'}")
        print(f"  Processed slices: {info['processed_slices']}")
        if not info['input_exists']:
            print(f"  Missing: {info['paths']['file_input']}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='XCAT Multi-timeframe Processor')
    parser.add_argument('--frame', type=int, help='Process single time frame')
    parser.add_argument('--status', action='store_true', help='Show processing status')
    parser.add_argument('--all', action='store_true', help='Process all time frames')
    
    args = parser.parse_args()
    
    if args.status:
        print_status_report()
    elif args.frame:
        success = process_single_timeframe(args.frame)
        sys.exit(0 if success else 1)
    elif args.all or len(sys.argv) == 1:
        success = main()
        sys.exit(0 if success else 1)
    else:
        parser.print_help()
