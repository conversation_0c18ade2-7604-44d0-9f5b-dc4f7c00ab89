# XCAT Multi-Timeframe Processing Pipeline Requirements

# Core scientific computing
numpy>=1.20.0
scipy>=1.7.0

# VTK for medical imaging
vtk>=9.0.0

# Parallel processing
joblib>=1.0.0

# Visualization (optional, for debugging)
matplotlib>=3.3.0

# Additional dependencies that may be needed
tqdm>=4.60.0

# Note: The following custom modules are required and should be available in the project:
# - myMorphingFunctions (from myWarpFunctions/ directory)
# - texturizationFunctions (from myTexturizer/ directory)
#
# These are not installable via pip and must be present in the project structure.
