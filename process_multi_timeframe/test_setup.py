#!/usr/bin/env python3
"""
Test script to verify the setup of the multi-timeframe processing pipeline
"""

import os
import sys

def test_imports():
    """Test that all modules can be imported successfully"""
    print("Testing module imports...")
    
    try:
        import config
        print("✅ config module imported successfully")
    except Exception as e:
        print(f"❌ Failed to import config: {e}")
        return False
    
    try:
        import utils
        print("✅ utils module imported successfully")
    except Exception as e:
        print(f"❌ Failed to import utils: {e}")
        return False
    
    try:
        import data_loader
        print("✅ data_loader module imported successfully")
    except Exception as e:
        print(f"❌ Failed to import data_loader: {e}")
        return False
    
    try:
        import slice_processor
        print("✅ slice_processor module imported successfully")
    except Exception as e:
        print(f"❌ Failed to import slice_processor: {e}")
        return False
    
    try:
        import binary_converter
        print("✅ binary_converter module imported successfully")
    except Exception as e:
        print(f"❌ Failed to import binary_converter: {e}")
        return False
    
    try:
        import main_processor
        print("✅ main_processor module imported successfully")
    except Exception as e:
        print(f"❌ Failed to import main_processor: {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration settings"""
    print("\nTesting configuration...")
    
    try:
        from config import SUBJECT_ID, TIME_FRAMES, BASE_DATA_PATH, BASE_OUTPUT_PATH
        print(f"✅ Subject ID: {SUBJECT_ID}")
        print(f"✅ Time frames: {TIME_FRAMES}")
        print(f"✅ Data path: {BASE_DATA_PATH}")
        print(f"✅ Output path: {BASE_OUTPUT_PATH}")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_path_generation():
    """Test path generation functions"""
    print("\nTesting path generation...")
    
    try:
        from config import get_paths_for_timeframe, TIME_FRAMES
        
        if TIME_FRAMES:
            test_frame = TIME_FRAMES[0]
            paths = get_paths_for_timeframe(test_frame)
            
            print(f"✅ Generated paths for frame {test_frame}:")
            for key, path in paths.items():
                print(f"   {key}: {path}")
            return True
        else:
            print("❌ No time frames configured")
            return False
    except Exception as e:
        print(f"❌ Path generation test failed: {e}")
        return False

def test_external_dependencies():
    """Test external dependencies"""
    print("\nTesting external dependencies...")
    
    # Test VTK
    try:
        import vtk
        print("✅ VTK imported successfully")
    except Exception as e:
        print(f"❌ VTK import failed: {e}")
        return False
    
    # Test NumPy
    try:
        import numpy as np
        print("✅ NumPy imported successfully")
    except Exception as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    # Test SciPy
    try:
        import scipy
        print("✅ SciPy imported successfully")
    except Exception as e:
        print(f"❌ SciPy import failed: {e}")
        return False
    
    # Test joblib
    try:
        import joblib
        print("✅ joblib imported successfully")
    except Exception as e:
        print(f"❌ joblib import failed: {e}")
        return False
    
    return True

def test_custom_modules():
    """Test custom module imports"""
    print("\nTesting custom module imports...")
    
    try:
        from utils import setup_module_paths, import_modules_safely
        
        # Setup paths
        setup_module_paths()
        print("✅ Module paths setup successfully")
        
        # Try importing custom modules
        try:
            MMF, defineTissueProperties, fixLVTexture = import_modules_safely()
            print("✅ Custom modules imported successfully")
            print(f"   MMF module: {type(MMF)}")
            print(f"   defineTissueProperties: {type(defineTissueProperties)}")
            print(f"   fixLVTexture: {type(fixLVTexture)}")
            return True
        except Exception as e:
            print(f"⚠️ Custom module import failed (this is expected if modules are not available): {e}")
            return True  # This is not a critical failure for setup testing
            
    except Exception as e:
        print(f"❌ Custom module test failed: {e}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    print("\nTesting file structure...")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    required_files = [
        'config.py',
        'utils.py', 
        'data_loader.py',
        'slice_processor.py',
        'binary_converter.py',
        'main_processor.py',
        '__init__.py',
        'README.md'
    ]
    
    all_exist = True
    for file in required_files:
        file_path = os.path.join(current_dir, file)
        if os.path.exists(file_path):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MISSING")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests"""
    print("="*60)
    print("XCAT Multi-Timeframe Processing Pipeline Setup Test")
    print("="*60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Module Imports", test_imports),
        ("Configuration", test_configuration),
        ("Path Generation", test_path_generation),
        ("External Dependencies", test_external_dependencies),
        ("Custom Modules", test_custom_modules)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"Running: {test_name}")
        print(f"{'='*40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The pipeline is ready to use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
