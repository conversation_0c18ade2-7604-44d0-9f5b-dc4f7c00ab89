#!/usr/bin/env python3
"""
Binary conversion functions for XCAT processing pipeline
Converts VTI files to binary format for further processing
"""

import os
import time
import numpy as np
import vtk
from vtk.util.numpy_support import vtk_to_numpy
from scipy.ndimage import zoom
from utils import check_binary_conversion_completion, filter_unconverted_slices
from config import BINARY_TARGET_SIZE

def vti2Bin_v2(input_folder, output_folder, target_size):
    """Convert VTI files to binary format with proper subfolder structure."""
    print(f"Converting VTI files from {input_folder} to binary in {output_folder}")
    
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # Process Model.vti file (mandatory)
    model_file = os.path.join(input_folder, "Model.vti")
    if os.path.exists(model_file):
        reader = vtk.vtkXMLImageDataReader()
        reader.SetFileName(model_file)
        reader.Update()
        model_data = reader.GetOutput()
        
        if model_data.GetPointData().HasArray('labels'):
            label_array = vtk_to_numpy(model_data.GetPointData().GetArray('labels'))
            label_dims = model_data.GetDimensions()
            labels = label_array.reshape(label_dims[0], label_dims[1], label_dims[2], order='F')
            
            # Resize to target dimensions if needed
            if labels.shape[0] != target_size[0] or labels.shape[1] != target_size[1]:
                zoom_factor = (target_size[0]/labels.shape[0], 
                            target_size[1]/labels.shape[1],
                            target_size[2]/labels.shape[2])
                labels = zoom(labels, zoom_factor, order=0)  # Order 0 for nearest neighbor
            
            # Save as binary file
            labels = labels.astype(np.uint8)
            labels.tofile(os.path.join(output_folder, "labels.bin"))
            print(f"✓ Saved labels.bin ({labels.shape})")
        else:
            print("✗ No 'labels' array found in Model.vti")
    else:
        print(f"✗ Model file not found: {model_file}")
        
    # Process tissue property files
    tissue_folder = os.path.join(os.path.dirname(input_folder), "Tissue_properties_initial_frame", os.path.basename(input_folder))
    
    for prop in ["PD", "T1", "T2", "T2s"]:
        # Create property-specific subfolder
        prop_output_folder = os.path.join(output_folder, prop.lower())
        if not os.path.exists(prop_output_folder):
            os.makedirs(prop_output_folder)
        
        # Try main results folder first
        prop_file = os.path.join(input_folder, f"{prop}.vti")
        
        # If not found, try tissue properties folder
        if not os.path.exists(prop_file):
            prop_file = os.path.join(tissue_folder, f"{prop}.vti")
        
        if os.path.exists(prop_file):
            reader = vtk.vtkXMLImageDataReader()
            reader.SetFileName(prop_file)
            reader.Update()
            prop_data = reader.GetOutput()
            
            # Extract property data - try both 'parameters' and property name
            array_name = None
            if prop_data.GetPointData().HasArray('parameters'):
                array_name = 'parameters'
            elif prop_data.GetPointData().HasArray(prop):
                array_name = prop
                
            if array_name:
                prop_array = vtk_to_numpy(prop_data.GetPointData().GetArray(array_name))
                prop_dims = prop_data.GetDimensions()
                prop_values = prop_array.reshape(prop_dims[0], prop_dims[1], prop_dims[2], order='F')
                
                # Resize to target dimensions if needed
                if prop_values.shape[0] != target_size[0] or prop_values.shape[1] != target_size[1]:
                    zoom_factor = (target_size[0]/prop_values.shape[0], 
                                target_size[1]/prop_values.shape[1],
                                target_size[2]/prop_values.shape[2])
                    prop_values = zoom(prop_values, zoom_factor, order=1)  # Order 1 for linear interpolation
                
                # Save as binary file in property-specific subfolder
                prop_values = prop_values.astype(np.float32)
                output_file = os.path.join(prop_output_folder, "value.bin")
                prop_values.tofile(output_file)
                print(f"✓ Saved {prop.lower()}/value.bin ({prop_values.shape})")
            else:
                print(f"✗ No suitable array found in {prop}.vti")
        else:
            print(f"✗ Property file not found: {prop_file}")

def convert_slice_to_binary(slice_idx, results_folder, output_folder_bin, target_size, runTexturizer):
    """Convert a single slice to binary format"""
    slice_output_folder_bin = f"{output_folder_bin}/slice_{slice_idx:03d}"
    slice_results_folder = f"{results_folder}/slice_{slice_idx:03d}"
    
    print(f"Converting slice {slice_idx} to binary format...")
    
    if not os.path.exists(slice_output_folder_bin):
        os.makedirs(slice_output_folder_bin)
        
    try:
        model_file = os.path.join(slice_results_folder, "Model.vti")
        if not os.path.exists(model_file):
            print(f"✗ Model.vti not found in {slice_results_folder}")
            return False
        else:
            vti2Bin_v2(slice_results_folder, slice_output_folder_bin, target_size)
            
            if check_binary_conversion_completion(slice_output_folder_bin, runTexturizer):
                print(f"✓ Slice {slice_idx} converted successfully")
                return True
            else:
                print(f"✗ Slice {slice_idx} conversion incomplete")
                return False
                
    except Exception as e:
        print(f"✗ Error converting slice {slice_idx}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def run_binary_conversion(z_indices, results_folder, output_folder_bin, runTexturizer, 
                         target_size=None, force_reconvert=False):
    """
    Run binary conversion for all specified slices
    
    Args:
        z_indices: List of slice indices to convert
        results_folder: Folder containing VTI results
        output_folder_bin: Output folder for binary files
        runTexturizer: Whether texturizer was run (affects file checking)
        target_size: Target dimensions for binary files
        force_reconvert: Whether to force reconversion of existing files
    """
    if target_size is None:
        target_size = BINARY_TARGET_SIZE
    
    print(f"\n🔄 Starting binary conversion...")
    
    # Check existing conversions
    if not force_reconvert:
        print("📋 Checking existing binary conversions...")
        z_indices_to_convert, converted_slices = filter_unconverted_slices(
            z_indices, output_folder_bin, runTexturizer
        )
        
        print(f"   Already converted: {len(converted_slices)} slices")
        print(f"   To convert: {len(z_indices_to_convert)} slices")
        
        if not z_indices_to_convert:
            print("🎉 All slices already converted!")
            return len(z_indices), 0
    else:
        z_indices_to_convert = z_indices
        converted_slices = []
        print("🔄 Force reconverting all files...")
    
    # Statistics
    conversion_start_time = time.time()
    successful_conversions = 0
    failed_conversions = 0
    
    # Convert slices
    for slice_idx in z_indices_to_convert:
        if convert_slice_to_binary(slice_idx, results_folder, output_folder_bin, 
                                 target_size, runTexturizer):
            successful_conversions += 1
        else:
            failed_conversions += 1
    
    # Print statistics
    conversion_time = time.time() - conversion_start_time
    total_slices = len(z_indices)
    already_converted = len(converted_slices) if not force_reconvert else 0
    total_successful = successful_conversions + already_converted
    
    print(f"\n🎉 === Binary Conversion Complete ===")
    print(f"📊 Statistics:")
    print(f"   Conversion time: {conversion_time/60:.1f} minutes")
    print(f"   Total slices: {total_slices}")
    if not force_reconvert:
        print(f"   Already converted: {already_converted} (skipped)")
    print(f"   Newly converted: {len(z_indices_to_convert)}")
    print(f"   Successful: {successful_conversions}")
    print(f"   Failed: {failed_conversions}")
    print(f"   Total successful: {total_successful}/{total_slices}")
    print(f"   Total success rate: {total_successful/total_slices*100:.1f}%")
    
    if failed_conversions > 0:
        print(f"   ⚠️ {failed_conversions} slices failed conversion, check logs")
    
    return successful_conversions, failed_conversions

def validate_binary_output(output_folder_bin, z_indices, runTexturizer):
    """Validate that binary conversion was successful for all slices"""
    print("\n🔍 Validating binary conversion results...")
    
    valid_slices = 0
    invalid_slices = 0
    
    for slice_idx in z_indices:
        slice_output_folder_bin = f"{output_folder_bin}/slice_{slice_idx:03d}"
        if check_binary_conversion_completion(slice_output_folder_bin, runTexturizer):
            valid_slices += 1
        else:
            invalid_slices += 1
            print(f"   ❌ Slice {slice_idx}: Incomplete binary conversion")
    
    print(f"Validation results:")
    print(f"   Valid: {valid_slices}/{len(z_indices)}")
    print(f"   Invalid: {invalid_slices}/{len(z_indices)}")
    print(f"   Success rate: {valid_slices/len(z_indices)*100:.1f}%")
    
    return valid_slices, invalid_slices

def get_binary_file_info(output_folder_bin, slice_idx):
    """Get information about binary files for a specific slice"""
    slice_output_folder_bin = f"{output_folder_bin}/slice_{slice_idx:03d}"
    
    if not os.path.exists(slice_output_folder_bin):
        return None
    
    info = {
        'slice_idx': slice_idx,
        'folder': slice_output_folder_bin,
        'files': {}
    }
    
    # Check labels.bin
    labels_file = os.path.join(slice_output_folder_bin, "labels.bin")
    if os.path.exists(labels_file):
        info['files']['labels'] = {
            'path': labels_file,
            'size': os.path.getsize(labels_file)
        }
    
    # Check tissue property files
    for prop in ["pd", "t1", "t2", "t2s"]:
        prop_file = os.path.join(slice_output_folder_bin, prop, "value.bin")
        if os.path.exists(prop_file):
            info['files'][prop] = {
                'path': prop_file,
                'size': os.path.getsize(prop_file)
            }
    
    return info
