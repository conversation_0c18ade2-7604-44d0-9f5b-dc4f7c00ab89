#!/usr/bin/env python3
"""
T2* update functionality for XCAT processing pipeline
Handles B0 field inhomogeneity effects on T2* values
"""

import os
import numpy as np
from scipy.ndimage import zoom
from tqdm import tqdm

def update_t2star_with_b0_3d(t2s_volume, t2_volume, b0_volume, voxel_size=2.0):
    """
    Update T2* values based on B0 field inhomogeneities using full 3D gradients
    
    Parameters:
    -----------
    t2s_volume : 3D array
        Intrinsic T2* values in milliseconds
    t2_volume : 3D array
        Intrinsic T2 values in milliseconds
    b0_volume : 3D array
        B0 field inhomogeneities in Hz
        
    Returns:
    --------
    t2star_volume : 3D array
        Updated T2* values in milliseconds
    """
    t2s_sec = t2s_volume / 1000.0
    # Calculate 1/T2* (original)
    r2_star_intial= 1.0 / (t2s_sec + 1e-10)

    # Convert T2 from ms to seconds
    t2_sec = t2_volume / 1000.0
    
    # Calculate 1/T2 (intrinsic relaxation rate)
    r2 = 1.0 / (t2_sec + 1e-10)

    #assuming original T2 star considering R2' contribution from diffusion
    r2prime_diffusion = r2_star_intial - r2 
    # r2prime_diffusion = 0
    
    # Scale the B0 field (multiply by 2 as you had in previous code)
    # b0_volume = b0_volume * 2
    
    # Convert voxel size to meters
    voxel_size_m = voxel_size / 1000.0
    
    # Calculate 3D B0 gradients using central differences (all directions)
    print("Calculating 3D field gradients...")
    gz, gy, gx = np.gradient(b0_volume, voxel_size_m)
    
    # Gradient magnitude in Hz/m (combining all directions)
    g_magnitude = np.sqrt(gx**2 + gy**2 + gz**2)
    
    # Constants
    gamma = 42.58e6  # gyromagnetic ratio in Hz/T
    D = 3e-9        # Diffusion coefficient of water at 37°C (m²/s)
    
    # Calculate R2' contribution from static dephasing
    r2prime_static = np.sqrt((g_magnitude * voxel_size_m)**2 / 12.0)
    print(f'complex calculation to get the range of r2prime-static: {np.min(r2prime_static):.2f}-{np.max(r2prime_static):.2f}')

    # r2prime_static = np.abs(b0_volume)
    r2prime_static = (np.pi * g_magnitude * voxel_size_m) / np.sqrt(6) *1.5
    print(f'simple calculation to get the range of r2prime-static: {np.min(r2prime_static):.2f}-{np.max(r2prime_static):.2f}')

    # Calculate R2' contribution from diffusion
    # r2prime_diffusion = (g_magnitude)**2 * D / 12.0
    
    # Total R2' is the sum of static and diffusion contributions
    r2prime_total = r2prime_static + r2prime_diffusion
    
    # Total R2* = R2 + R2'
    r2_star = r2 + r2prime_total
    
    # Convert back to T2* in milliseconds
    t2star_sec = 1.0 / (r2_star + 1e-10)
    t2star_ms = t2star_sec * 1000.0
    
    # Apply reasonable limits
    t2star_ms = np.clip(t2star_ms, 1.0, 66.0)
    
    return t2star_ms

def process_t2star_volume_3d(t2s_volume, t2_volume, b0_map_path, output_folder=None, z_indices=None, labels_volume=None, 
                           b0_start_slice=199, b0_end_slice=306):
    """
    Process the entire T2* volume with B0 field inhomogeneities using full 3D processing
    
    Parameters:
    -----------
    t2s_volume : 3D numpy array
        The original T2* volume
    t2_volume : 3D numpy array
        The original T2 volume
    b0_map_path : str
        Path to the B0 map file (.npy)
    output_folder : str, optional
        If provided, saves updated T2* maps back to binary files
    z_indices : list, optional
        Original slice indices corresponding to the volume slices
    labels_volume : 3D numpy array, optional
        Volume of tissue labels for visualization
    b0_start_slice : int
        Starting slice index in B0 map that corresponds to the heart region
    b0_end_slice : int
        Ending slice index in B0 map that corresponds to the heart region
        
    Returns:
    --------
    t2star_plus_volume : 3D numpy array
        Updated T2* volume with B0 effects
    """
    print(f"Loading B0 map from: {b0_map_path}")
    
    # Load B0 map
    if not os.path.exists(b0_map_path):
        print(f"Warning: B0 map not found at {b0_map_path}")
        return t2s_volume  # Return original if no B0 map
    
    b0_map = np.load(b0_map_path)
    print(f"Original B0 map shape: {b0_map.shape}")
    
    # Extract the relevant slices from B0 map
    print(f"Extracting B0 map slices {b0_start_slice} to {b0_end_slice}")
    b0_extracted = b0_map[:, :, b0_start_slice:b0_end_slice+1]
    
    print(f"T2* volume shape: {t2s_volume.shape}")
    print(f"Extracted B0 map shape: {b0_extracted.shape}")
    
    # Apply orientation adjustments to match T2* volume
    print("Applying orientation adjustments to B0 map...")
    # Flip along the z-axis to match XCAT orientation
    b0_extracted = np.flip(b0_extracted, axis=2)
    
    # Resize B0 map to match T2* volume dimensions if needed
    if b0_extracted.shape != t2s_volume.shape:
        print(f"Resizing B0 map from {b0_extracted.shape} to {t2s_volume.shape}")
        zoom_factors = [t2s_volume.shape[i] / b0_extracted.shape[i] for i in range(3)]
        b0_extracted = zoom(b0_extracted, zoom_factors, order=1)
    
    # Process the entire volume with 3D B0 effects
    print("Processing entire T2* volume with 3D B0 effects...")
    t2star_plus_volume = update_t2star_with_b0_3d(t2s_volume, t2_volume, b0_extracted)
    
    # Save updated T2* slices if output folder is provided
    if output_folder and z_indices is not None and len(z_indices) > 0:
        print("Saving updated T2*+ slices to binary files...")
        save_updated_t2star_slices(t2star_plus_volume, output_folder, z_indices)
    
    return t2star_plus_volume

def save_updated_t2star_slices(t2star_plus_volume, output_folder, z_indices):
    """
    Save updated T2* slices back to binary files
    
    Parameters:
    -----------
    t2star_plus_volume : 3D numpy array
        Updated T2* volume
    output_folder : str
        Output folder path
    z_indices : list
        Original slice indices
    """
    for i, slice_idx in enumerate(tqdm(z_indices, desc="Saving slices")):
        slice_folder = os.path.join(output_folder, f"slice_{slice_idx:03d}")
        
        # Create tissue properties folder if it doesn't exist
        tissue_folder = os.path.join(slice_folder, "Tissue_properties_initial_frame")
        os.makedirs(tissue_folder, exist_ok=True)
        
        # Save T2*+ slice
        t2star_plus_slice = t2star_plus_volume[:, :, i]
        t2star_plus_file = os.path.join(tissue_folder, "T2s_plus.bin")
        
        # Save as binary file (float32)
        t2star_plus_slice.astype(np.float32).tofile(t2star_plus_file)
        
        print(f"Saved T2*+ slice {slice_idx} to {t2star_plus_file}")

def load_t2star_volume_from_slices(results_folder, z_indices, param_name="T2s"):
    """
    Load T2* volume from individual slice files
    
    Parameters:
    -----------
    results_folder : str
        Path to results folder
    z_indices : list
        List of slice indices
    param_name : str
        Parameter name ("T2s" or "T2")
        
    Returns:
    --------
    volume : 3D numpy array
        Loaded volume
    """
    # Get dimensions from first slice
    first_slice_folder = os.path.join(results_folder, f"slice_{z_indices[0]:03d}", "Tissue_properties_initial_frame")
    first_slice_file = os.path.join(first_slice_folder, f"{param_name}.bin")
    
    if not os.path.exists(first_slice_file):
        raise FileNotFoundError(f"Could not find {first_slice_file}")
    
    # Load first slice to get dimensions
    first_slice = np.fromfile(first_slice_file, dtype=np.float32)
    slice_shape = int(np.sqrt(len(first_slice)))  # Assuming square slices
    first_slice = first_slice.reshape(slice_shape, slice_shape)
    
    # Initialize volume
    volume = np.zeros((slice_shape, slice_shape, len(z_indices)), dtype=np.float32)
    volume[:, :, 0] = first_slice
    
    # Load remaining slices
    for i, slice_idx in enumerate(z_indices[1:], 1):
        slice_folder = os.path.join(results_folder, f"slice_{slice_idx:03d}", "Tissue_properties_initial_frame")
        slice_file = os.path.join(slice_folder, f"{param_name}.bin")
        
        if os.path.exists(slice_file):
            slice_data = np.fromfile(slice_file, dtype=np.float32)
            slice_data = slice_data.reshape(slice_shape, slice_shape)
            volume[:, :, i] = slice_data
        else:
            print(f"Warning: Could not find {slice_file}")
    
    return volume
