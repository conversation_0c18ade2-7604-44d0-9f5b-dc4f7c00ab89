#!/usr/bin/env python3
"""
Slice processing functions for XCAT processing pipeline
Handles individual slice processing with warping and texturization
"""

import os
import sys
import numpy as np
import time
from joblib import Parallel, delayed
from utils import (setup_module_paths, import_modules_safely, add_functions_to_mmf, 
                   setup_matplotlib_backend, check_slice_completion, 
                   filter_unprocessed_slices, print_processing_statistics)

def process_single_slice(slice_idx, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                        results_folder, warped_images, runWarper, runTexturizer, network_path,
                        voxelNumpyToVTK, parentVTI, force_reprocess=False):
    """Process a single slice with warping and texturization"""
    try:
        if not force_reprocess and check_slice_completion(slice_idx, results_folder, runWarper, runTexturizer):
            print(f"✅ Slice {slice_idx} already processed, skipping...")
            return True
            
        print(f"Processing slice {slice_idx}...")

        # Setup module paths and imports
        setup_module_paths()
        MMF, defineTissueProperties, fixLVTexture = import_modules_safely()
        
        # Setup matplotlib for non-interactive use
        plt = setup_matplotlib_backend()

        # Create slice-specific output directories
        slice_results_folder = f"{results_folder}/slice_{slice_idx:03d}"
        slice_warped_images = f"{warped_images}/slice_{slice_idx:03d}"
        slice_tissue_folder = f"{results_folder}/Tissue_properties_initial_frame/slice_{slice_idx:03d}"
        
        for folder in [slice_results_folder, slice_warped_images, slice_tissue_folder]:
            if not os.path.exists(folder):
                os.makedirs(folder, exist_ok=True)
        
        if runWarper:
            # Process slice
            slice_data = np.zeros((dim[0], dim[1], 1))
            slice_data[:,:,0] = labels[:,:,slice_idx]
            
            # Use MMF's warpSlice function
            data_input_slice, Target_mask_slice, BP_fill, mapx0, mapy0, centerBox_x, centerBox_y = MMF.warpSlice(
                Target_mask, data_input, slice_idx, maskLabels, True)
            
            # VTK processing
            MMF = add_functions_to_mmf(MMF)

            img_ref = MMF.vtkSliceImage(data_input_slice, spacing, 
                                       [orig_image[0], orig_image[1], orig_image[2] + slice_idx*spacing[2]])
            img_ref = MMF.addArrayToVtk(img_ref, data_input_slice, 'labels', False)
            img_ref = MMF.addArrayToVtk(img_ref, Target_mask_slice, 'LV_mask', False)
            
            model_path = os.path.join(slice_results_folder, "Model.vti")
            MMF.saveVTI(img_ref, model_path)
            par_img = img_ref
            
            if runTexturizer:
                # Use Texturizer to generate tissue properties
                PD0, T10, T20, T2s0 = defineTissueProperties(par_img, network_path)
                
                textureLV = False
                if not textureLV:
                    PD0, T10, T20, T2s0, LV_prop_vals = fixLVTexture(
                        data_input_slice, PD0, T10, T20, T2s0, 
                        maskLabels, which_prop='meanLV'
                    )
                
                # Clean up unnecessary arrays
                arrays_to_remove = ['dx_beating', 'dy_beating', 'dz_beating', 
                                   'dx_breathing', 'dy_breathing', 'dz_breathing', 
                                   'maskValues', 'parameters']
                for arr_n in arrays_to_remove:
                    if par_img.GetPointData().GetArray(arr_n):
                        par_img.GetPointData().RemoveArray(arr_n)
                        par_img.Modified()

                # Save tissue properties
                par_img = MMF.addArrayToVtk(par_img, PD0, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/PD.vti")
                
                par_img = MMF.addArrayToVtk(par_img, T10, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/T1.vti")
                
                par_img = MMF.addArrayToVtk(par_img, T20, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/T2.vti")
                
                par_img = MMF.addArrayToVtk(par_img, T2s0, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/T2s.vti")
                
                print(f"Slice {slice_idx} tissue properties:")
                print(f"  PD range: {np.min(PD0):.2f}-{np.max(PD0):.2f}")
                print(f"  T1 range: {np.min(T10):.2f}-{np.max(T10):.2f}")
                print(f"  T2 range: {np.min(T20):.2f}-{np.max(T20):.2f}")
                print(f"  T2* range: {np.min(T2s0):.2f}-{np.max(T2s0):.2f}")
                
        print(f"✅ Successfully processed slice {slice_idx}")
        return True
        
    except Exception as e:
        print(f"❌ Error processing slice {slice_idx}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def run_parallel_processing(data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                           results_folder, warped_images, runWarper, runTexturizer, network_path,
                           z_indices, voxelNumpyToVTK, parentVTI, n_jobs=4, check_existing=True, 
                           force_reprocess=False):
    """Run parallel processing with automatic retry and file checking"""
    print(f"🚀 Starting parallel processing of {len(z_indices)} slices using {n_jobs} processes")
    
    if check_existing and not force_reprocess:
        print("\n📋 Checking existing files...")
        unprocessed_slices, completed_slices = filter_unprocessed_slices(
            z_indices, results_folder, runWarper, runTexturizer
        )
        
        print(f"   Completed: {len(completed_slices)} slices")
        print(f"   To process: {len(unprocessed_slices)} slices")
        
        if not unprocessed_slices:
            print("🎉 All slices already completed!")
            return [True] * len(z_indices)
            
        z_indices_to_process = unprocessed_slices
    else:
        z_indices_to_process = z_indices
        completed_slices = []
    
    start_time = time.time()
    
    if z_indices_to_process:
        print(f"\n📋 Testing single slice to verify environment...")
        test_slice = sorted(z_indices_to_process)[0]
        test_result = process_single_slice(
            test_slice, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
            results_folder, warped_images, runWarper, runTexturizer, network_path,
            voxelNumpyToVTK, parentVTI, force_reprocess
        )
        
        if not test_result:
            print("❌ Test slice failed! Check environment configuration.")
            return []
        
        print("✅ Test successful! Starting parallel processing...")
        
        results = Parallel(n_jobs=n_jobs, verbose=10)(
            delayed(process_single_slice)(
                slice_idx, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                results_folder, warped_images, runWarper, runTexturizer, network_path,
                voxelNumpyToVTK, parentVTI, force_reprocess
            ) for slice_idx in sorted(z_indices_to_process)
        )
    else:
        results = []
    
    total_time = time.time() - start_time
    total_slices = len(z_indices)
    processed_slices = len(z_indices_to_process)
    successful_processed = sum(1 for r in results if r) if results else 0
    already_completed = len(completed_slices)
    
    print_processing_statistics(total_time, total_slices, processed_slices, 
                              successful_processed, already_completed)
    
    # Handle failed slices
    if z_indices_to_process:
        failed_slices = [z_indices_to_process[i] for i, r in enumerate(results) if not r]
        if failed_slices:
            print(f"   Failed slices: {failed_slices}")
            
            # Retry failed slices if failure rate is low
            if len(failed_slices) < processed_slices * 0.2:  # Less than 20% failure
                print(f"\n🔄 Retrying {len(failed_slices)} failed slices...")
                retry_results = []
                for slice_idx in failed_slices:
                    print(f"Retrying slice {slice_idx}...")
                    result = process_single_slice(
                        slice_idx, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                        results_folder, warped_images, runWarper, runTexturizer, network_path,
                        voxelNumpyToVTK, parentVTI, force_reprocess
                    )
                    retry_results.append(result)
                
                retry_successful = sum(1 for r in retry_results if r)
                print(f"Retry results: {retry_successful}/{len(failed_slices)} successful")
    
    # Return combined results
    all_results = []
    processed_idx = 0
    for slice_idx in z_indices:
        if slice_idx in completed_slices:
            all_results.append(True)
        else:
            all_results.append(results[processed_idx] if processed_idx < len(results) else False)
            processed_idx += 1
    
    return all_results
