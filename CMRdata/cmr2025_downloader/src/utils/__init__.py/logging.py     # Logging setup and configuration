import logging
import os

# Set up logging directory
log_dir = '/mnt/LiDXXLab_Files/ziyang/CMR2025/logs'
os.makedirs(log_dir, exist_ok=True)

# Configure logging
log_file = os.path.join(log_dir, 'cmr2025_download.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logging.info("Logging is set up and ready to use.")