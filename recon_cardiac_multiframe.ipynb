import numpy as np
import matplotlib.pyplot as plt
import os
from matplotlib.patches import Patch

# Define the number of time frames to load
n_frames = 2

# Base path for cardiac data
base_path = "/home/<USER>/XCAT_Project/outputData/female_151/cine"

# Load multiple time frames
print(f"Loading {n_frames} cardiac parameter maps...")
frame_data = []
for frame_idx in range(1, n_frames + 1):
    frame_path = os.path.join(base_path, f"frame_{frame_idx:02d}", "cardiac_region_new", "cardiac_volume.npz")
    print(f"Loading frame {frame_idx}: {frame_path}")
    data = np.load(frame_path)
    frame_data.append(data)
    print(f"  Frame {frame_idx} shape: {data['Labels'].shape}")

# Use the first frame for the main visualization
data = frame_data[0]

# Display available parameters
print(f"\nAvailable parameters: {list(data.keys())}")

# Get the middle slice index for visualization
z_slice = data['Labels'].shape[2] // 2
print(f"Displaying slice {z_slice} of {data['Labels'].shape[2]}")

# Define parameter visualization settings
param_settings = {
    'PD': {'cmap': 'gray', 'title': 'Proton Density', 'vmin': None, 'vmax': None},
    'T1': {'cmap': 'hot', 'title': 'T1 (ms)', 'vmin': 0, 'vmax': 2000},
    'T2': {'cmap': 'viridis', 'title': 'T2 (ms)', 'vmin': 0, 'vmax': 150},
    'T2star': {'cmap': 'inferno', 'title': 'T2* (ms)', 'vmin': 0, 'vmax': 100},
    'T2star_plus': {'cmap': 'inferno', 'title': 'T2*+ (ms)', 'vmin': 0, 'vmax': 100},
    'Labels': {'cmap': 'tab10', 'title': 'Tissue Labels', 'vmin': None, 'vmax': None},
    'B0': {'cmap': 'jet', 'title': 'B0 Field (Hz)', 'vmin': -400, 'vmax': 400},
    'ShimmedB0': {'cmap': 'jet', 'title': 'Shim B0 Field', 'vmin': -400, 'vmax': 400}
}

# Create figure for visualization of Frame 1
n_params = len(param_settings)
n_cols = 3  # Define number of columns
n_rows = (n_params + n_cols - 1) // n_cols  # Calculate required rows

plt.figure(figsize=(15, 4 * n_rows))
plt.suptitle('Frame 1 - All Parameters', fontsize=16, y=1.02)

# Display each parameter map for Frame 1
for i, (param_name, settings) in enumerate(param_settings.items()):
    plt.subplot(n_rows, n_cols, i + 1)
    
    # Check if parameter exists in the data
    if param_name in data:
        param_data = data[param_name]
        # Get image slice
        image_slice = param_data[:, :, z_slice]
        
        # Display the image
        im = plt.imshow(image_slice, 
                        cmap=settings['cmap'],
                        vmin=settings['vmin'],
                        vmax=settings['vmax'])
        
        plt.colorbar(im, fraction=0.046, pad=0.04)
        plt.title(f"{settings['title']}\n(min: {np.min(image_slice):.1f}, max: {np.max(image_slice):.1f})")
    else:
        plt.text(0.5, 0.5, f"{param_name} not found",
                 ha='center', va='center',
                 transform=plt.gca().transAxes)
        plt.title(f"Missing: {settings['title']}")
    
    plt.axis('off')

# Adjust layout and display
plt.tight_layout()
plt.show()

# Display cardiac anatomy with B0 overlay for Frame 1
if 'Labels' in data and 'B0' in data:
    plt.figure(figsize=(10, 8))
    
    # Create a mask for cardiac structures
    cardiac_mask = np.zeros_like(data['Labels'][:,:,z_slice], dtype=bool)
    for label in [1, 2, 5, 6]:  # LV wall, RV wall, LV blood, RV blood
        cardiac_mask |= (data['Labels'][:,:,z_slice] == label)
    
    # Create a color-coded anatomy image
    anatomy = np.zeros((*data['Labels'][:,:,z_slice].shape, 3))
    anatomy[data['Labels'][:,:,z_slice] == 1] = [1, 0, 0]  # LV wall - red
    anatomy[data['Labels'][:,:,z_slice] == 5] = [1, 0.7, 0.7]  # LV blood - light red
    anatomy[data['Labels'][:,:,z_slice] == 2] = [0, 0, 1]  # RV wall - blue
    anatomy[data['Labels'][:,:,z_slice] == 6] = [0.7, 0.7, 1]  # RV blood - light blue
    

    
    # Display base anatomy
    plt.imshow(anatomy)
    
    # Overlay B0 field on cardiac region
    b0_display = data['B0'][:,:,z_slice].copy()
    b0_display[~cardiac_mask] = np.nan  # Make non-cardiac regions transparent
    
    b0_overlay = plt.imshow(b0_display, cmap='coolwarm', alpha=0.7,
                            vmin=-30, vmax=30)
    plt.colorbar(b0_overlay, label='B0 Field (Hz)')
    
    plt.title('Frame 1 - B0 Field Overlay on Cardiac Anatomy')
    plt.axis('off')
    plt.tight_layout()
    plt.show()

# New visualization: T2 comparison across multiple time frames
if all('T2' in frame for frame in frame_data):
    plt.figure(figsize=(15, 5))
    plt.suptitle(f'T2 Maps Comparison Across {n_frames} Time Frames (Slice {z_slice})', fontsize=16)
    
    # Find consistent vmin and vmax for T2 across all frames
    t2_min = min(np.min(frame['T2'][:, :, z_slice]) for frame in frame_data)
    t2_max = max(np.max(frame['T2'][:, :, z_slice]) for frame in frame_data)
    
    for i, frame in enumerate(frame_data):
        plt.subplot(1, n_frames, i + 1)
        
        # Get T2 slice for this frame
        t2_slice = frame['T2'][:, :, z_slice]
        
        # Display T2 map
        im = plt.imshow(t2_slice, cmap='viridis', vmin=0, vmax=150)
        plt.colorbar(im, fraction=0.046, pad=0.04)
        plt.title(f'Frame {i+1}\n(min: {np.min(t2_slice):.1f}, max: {np.max(t2_slice):.1f} ms)')
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Additional visualization: T2 difference maps between consecutive frames
    if n_frames > 1:
        plt.figure(figsize=(15, 5))
        plt.suptitle('T2 Temporal Changes Between Consecutive Frames', fontsize=16)
        
        for i in range(n_frames - 1):
            plt.subplot(1, n_frames - 1, i + 1)
            
            # Calculate difference between consecutive frames
            t2_diff = frame_data[i+1]['T2'][:, :, z_slice] - frame_data[i]['T2'][:, :, z_slice]
            
            # Display difference map
            im = plt.imshow(t2_diff, cmap='RdBu_r', vmin=-10, vmax=10)
            plt.colorbar(im, fraction=0.046, pad=0.04, label='ΔT2 (ms)')
            plt.title(f'Frame {i+2} - Frame {i+1}\n(mean Δ: {np.mean(t2_diff):.2f} ms)')
            plt.axis('off')
        
        plt.tight_layout()
        plt.show()

# Visualization: Label (structure) comparison across multiple time frames
if all('Labels' in frame for frame in frame_data):
    plt.figure(figsize=(15, 5))
    plt.suptitle(f'Cardiac Structure (Labels) Comparison Across {n_frames} Time Frames (Slice {z_slice})', fontsize=16)
    
    for i, frame in enumerate(frame_data):
        plt.subplot(1, n_frames, i + 1)
        
        # Get Labels slice for this frame
        labels_slice = frame['Labels'][:, :, z_slice]
        
        # Create a color-coded anatomy image
        anatomy = np.zeros((*labels_slice.shape, 3))
        anatomy[labels_slice == 1] = [1, 0, 0]  # LV wall - red
        anatomy[labels_slice == 5] = [1, 0.7, 0.7]  # LV blood - light red
        anatomy[labels_slice == 2] = [0, 0, 1]  # RV wall - blue
        anatomy[labels_slice == 6] = [0.7, 0.7, 1]  # RV blood - light blue
        anatomy[labels_slice == 3] = [0, 1, 0]  # Other tissue - green
        anatomy[labels_slice == 4] = [1, 1, 0]  # Other tissue - yellow
        
        # Display anatomy
        plt.imshow(anatomy)
        plt.title(f'Frame {i+1}')
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Additional visualization: Label changes between consecutive frames
    if n_frames > 1:
        plt.figure(figsize=(15, 5))
        plt.suptitle('Structural Changes Between Consecutive Frames', fontsize=16)
        
        for i in range(n_frames - 1):
            plt.subplot(1, n_frames - 1, i + 1)
            
            # Get label slices
            labels1 = frame_data[i]['Labels'][:, :, z_slice]
            labels2 = frame_data[i+1]['Labels'][:, :, z_slice]
            
            # Create difference visualization
            # Changed pixels will be highlighted
            diff_image = np.zeros((*labels1.shape, 3))
            unchanged = (labels1 == labels2)
            changed = ~unchanged
            
            # Show unchanged regions in gray
            diff_image[unchanged] = [0.8, 0.8, 0.8]
            
            # Highlight changed regions
            # From LV wall to other
            diff_image[(labels1 == 1) & changed] = [1, 0, 0]  # Red
            # From RV wall to other
            diff_image[(labels1 == 2) & changed] = [0, 0, 1]  # Blue
            # From blood to other
            diff_image[((labels1 == 5) | (labels1 == 6)) & changed] = [1, 1, 0]  # Yellow
            # New cardiac tissue
            diff_image[((labels2 == 1) | (labels2 == 2) | (labels2 == 5) | (labels2 == 6)) & changed & 
                      ~((labels1 == 1) | (labels1 == 2) | (labels1 == 5) | (labels1 == 6))] = [0, 1, 0]  # Green
            
            plt.imshow(diff_image)
            
            # Calculate statistics
            n_changed = np.sum(changed)
            pct_changed = 100 * n_changed / changed.size
            
            plt.title(f'Frame {i+2} vs Frame {i+1}\n({n_changed} pixels changed, {pct_changed:.1f}%)')
            plt.axis('off')
            
            # Add legend
            if i == 0:
                legend_elements = [
                    Patch(facecolor=[0.8, 0.8, 0.8], label='Unchanged'),
                    Patch(facecolor=[1, 0, 0], label='LV wall changed'),
                    Patch(facecolor=[0, 0, 1], label='RV wall changed'),
                    Patch(facecolor=[1, 1, 0], label='Blood changed'),
                    Patch(facecolor=[0, 1, 0], label='New cardiac tissue')
                ]
                plt.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1, 0.5))
        
        plt.tight_layout()
        plt.show()

# Calculate structure volumes across frames
print("\nCardiac Structure Volumes Across Time Frames:")
print("-" * 60)
label_names = {1: 'LV wall', 2: 'RV wall', 5: 'LV blood', 6: 'RV blood'}

for label_id, label_name in label_names.items():
    print(f"\n{label_name}:")
    volumes = []
    for i, frame in enumerate(frame_data):
        # Count voxels for this label in the entire volume
        voxel_count = np.sum(frame['Labels'] == label_id)
        volumes.append(voxel_count)
        print(f"  Frame {i+1}: {voxel_count:,} voxels")
    
    # Calculate volume changes
    if len(volumes) > 1:
        for i in range(1, len(volumes)):
            change = volumes[i] - volumes[i-1]
            pct_change = 100 * change / volumes[i-1]
            print(f"    Change from Frame {i} to {i+1}: {change:+,} voxels ({pct_change:+.1f}%)")

# Print statistics for T2 values across frames
print("\nT2 Statistics Across Time Frames:")
print("-" * 50)
for i, frame in enumerate(frame_data):
    t2_slice = frame['T2'][:, :, z_slice]
    print(f"Frame {i+1}:")
    print(f"  Mean T2: {np.mean(t2_slice):.2f} ms")
    print(f"  Std T2:  {np.std(t2_slice):.2f} ms")
    print(f"  Min T2:  {np.min(t2_slice):.2f} ms")
    print(f"  Max T2:  {np.max(t2_slice):.2f} ms")

# import numpy as np
# import matplotlib.pyplot as plt
# import os
from matplotlib.animation import FuncAnimation
from matplotlib.patches import Patch
from IPython.display import HTML

def simulate_mri_signal(pd_map, t1_map, t2_map, t2star_map, b0_map, labels_map, TR, TE, flip_angle, sequence_type='spin_echo'):
    """
    Generate synthetic MRI images from tissue property maps
    
    Parameters:
    -----------
    pd_map, t1_map, t2_map, t2star_map, b0_map : 2D or 3D arrays
        Tissue property maps
    labels_map : 2D or 3D array
        Tissue labels (0 = background)
    TR : float
        Repetition time in ms
    TE : float
        Echo time in ms
    flip_angle : float
        Flip angle in degrees
    sequence_type : str
        'spin_echo', 'gradient_echo', or 'bssfp'
    
    Returns:
    --------
    image : array
        Simulated MR image (magnitude)
    """
    # Convert flip angle to radians if provided in degrees
    if flip_angle > 6.28:  # If larger than 2π, assume it's in degrees
        flip_angle = np.deg2rad(flip_angle)
    
    # Create image containers
    signal = np.zeros_like(pd_map, dtype=complex)
    
    if sequence_type.lower() == 'spin_echo':
        # T2-weighted spin echo: S = PD * (1-exp(-TR/T1)) * exp(-TE/T2)
        t1_factor = 1 - np.exp(-TR / (t1_map + 1e-6))
        t2_factor = np.exp(-TE / (t2_map + 1e-6))
        signal = pd_map * t1_factor * t2_factor
        
    elif sequence_type.lower() == 'gradient_echo':
        # Gradient echo: S = PD * sin(α) * (1-exp(-TR/T1)) / (1-cos(α)*exp(-TR/T1)) * exp(-TE/T2*)
        t1_factor = np.sin(flip_angle) * (1 - np.exp(-TR / (t1_map + 1e-6)))
        denominator = 1 - np.cos(flip_angle) * np.exp(-TR / (t1_map + 1e-6))
        t2star_factor = np.exp(-TE / (t2star_map + 1e-6))
        signal = pd_map * (t1_factor / (denominator + 1e-6)) * t2star_factor
        # In the gradient_echo case:
        b0_phase = 2 * np.pi * b0_map * TE / 1000.0  # B0 in Hz, TE in ms
        signal = signal * np.exp(1j * b0_phase)  # Add B0-induced phase
        
    elif sequence_type.lower() == 'bssfp':
        # Simplified bSSFP signal equation
        t1_t2_ratio = t1_map / (t2_map + 1e-6)
        signal = pd_map * np.sin(flip_angle) / ((t1_t2_ratio + 1) - np.cos(flip_angle) * (t1_t2_ratio - 1)) * np.exp(-TE/(t2_map + 1e-6))
    
    # Add some realistic phase variation
    phase_noise = np.random.normal(0, 0.1, pd_map.shape)  # Adjust standard deviation as needed
    signal = signal * np.exp(1j * phase_noise)
    
    # Apply background mask - set background (label=0) to very low signal
    magnitude = np.abs(signal)
    magnitude[labels_map == 0] = magnitude[labels_map == 0] * 0.05  # Make background very dark
    
    return magnitude  # Return magnitude image


# Define sequence parameters
sequences = {
    'T2w_SE': {
        'sequence_type': 'spin_echo',
        'TR': 1850,  # ms
        'TE': 80,    # ms
        'flip_angle': 90,  # degrees
        'description': 'T2-weighted Spin Echo'
    },
    'T1w_GRE': {
        'sequence_type': 'gradient_echo',
        'TR': 4,    # ms
        'TE': 1.7,     # ms
        'flip_angle': 15,  # degrees
        'description': 'T1-weighted Gradient Echo'
    },
    'bSSFP': {
        'sequence_type': 'bssfp',
        'TR': 3,     # ms
        'TE': 1.4,     # ms
        'flip_angle': 45,  # degrees
        'description': 'Balanced SSFP'
    }
}

# Generate weighted images for all frames
weighted_images = {}

for seq_name, seq_params in sequences.items():
    print(f"\nGenerating {seq_params['description']} images...")
    weighted_images[seq_name] = []
    
    for i, frame in enumerate(frame_data):
        # Get parameter maps for this frame
        pd_map = frame['PD']
        t1_map = frame['T1']
        t2_map = frame['T2']
        t2star_map = frame['T2star'] if 'T2star' in frame else frame['T2star_plus']
        b0_map = frame['B0'] if 'B0' in frame else np.zeros_like(pd_map)
        
        # Generate weighted image for the entire 3D volume
        weighted_image = simulate_mri_signal(
            pd_map, t1_map, t2_map, t2star_map, b0_map, frame['Labels'],
            TR=seq_params['TR'],
            TE=seq_params['TE'],
            flip_angle=seq_params['flip_angle'],
            sequence_type=seq_params['sequence_type']
        )
        
        weighted_images[seq_name].append(weighted_image)
        print(f"  Frame {i+1} completed. Image shape: {weighted_image.shape}")

# Visualization: Display different sequences for Frame 1
frame_to_show = 0  # Show first frame
z_slice = frame_data[0]['Labels'].shape[2] // 2  # Middle slice

plt.figure(figsize=(15, 5))
plt.suptitle(f'Simulated MRI Images - Frame {frame_to_show + 1}, Slice {z_slice}', fontsize=16)

for i, (seq_name, seq_params) in enumerate(sequences.items()):
    plt.subplot(1, len(sequences), i + 1)
    
    # Get the weighted image for this sequence
    image_slice = weighted_images[seq_name][frame_to_show][:, :, z_slice]
    
    # Display the image
    im = plt.imshow(image_slice, cmap='gray')
    plt.colorbar(im, fraction=0.046, pad=0.04)
    
    # Add sequence information to title
    title = f"{seq_params['description']}\n"
    title += f"TR/TE: {seq_params['TR']}/{seq_params['TE']} ms, FA: {seq_params['flip_angle']}°\n"
    title += f"Signal range: [{np.min(image_slice):.1f}, {np.max(image_slice):.1f}]"
    plt.title(title, fontsize=10)
    plt.axis('off')

plt.tight_layout()
plt.show()

# Animated cine display of T2w SE across all time frames
from matplotlib.animation import FuncAnimation
from IPython.display import HTML

seq_to_animate = 'T2w_SE'
fig, ax = plt.subplots(figsize=(8, 8))
fig.suptitle(f'{sequences[seq_to_animate]["description"]} Cine (Slice {z_slice})', fontsize=16)

# Find consistent display range for animation
all_images = [img[:, :, z_slice] for img in weighted_images[seq_to_animate]]
vmin = min(np.percentile(img, 1) for img in all_images)
vmax = max(np.percentile(img, 99) for img in all_images)

# Initial image
im = ax.imshow(weighted_images[seq_to_animate][0][:, :, z_slice], 
               cmap='gray', vmin=vmin, vmax=vmax)
cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
ax.axis('off')
frame_text = ax.text(0.02, 0.98, '', transform=ax.transAxes, 
                     color='white', fontsize=12, va='top',
                     bbox=dict(boxstyle='round', facecolor='black', alpha=0.5))

def animate(frame):
    print(f"Animating frame {frame}")  # Debug print
    im.set_array(weighted_images[seq_to_animate][frame][:, :, z_slice])
    frame_text.set_text(f'Frame {frame + 1}/{len(weighted_images[seq_to_animate])}')
    return [im, frame_text]

# Create animation
anim = FuncAnimation(fig, animate, frames=n_frames, interval=200, blit=True, repeat=True)
from IPython.display import display
anim_html = HTML(anim.to_html5_video())
display(anim_html)

# For static display of cardiac cycle
plt.figure(figsize=(15, 5))
plt.suptitle(f'{sequences[seq_to_animate]["description"]} Cardiac Cycle (Slice {z_slice})', fontsize=16)

for i in range(n_frames):
    plt.subplot(1, n_frames, i + 1)
    
    # Get image slice
    image_slice = weighted_images[seq_to_animate][i][:, :, z_slice]
    
    # Display
    im = plt.imshow(image_slice, cmap='gray', vmin=vmin, vmax=vmax)
    plt.colorbar(im, fraction=0.046, pad=0.04)
    plt.title(f'Frame {i+1}')
    plt.axis('off')

plt.tight_layout()
plt.show()

# Visualization: Show cardiac anatomy with overlaid weighted image
frame_idx = 0
seq_name = 'bSSFP'  # bSSFP typically provides good blood-myocardium contrast

plt.figure(figsize=(12, 5))

# Left panel: Anatomical labels
plt.subplot(1, 2, 1)
labels_slice = frame_data[frame_idx]['Labels'][:, :, z_slice]

# Create color-coded anatomy for all cardiac components
anatomy = np.zeros((*labels_slice.shape, 3))
anatomy[labels_slice == 1] = [1, 0, 0]      # LV wall - red
anatomy[labels_slice == 2] = [0, 0, 1]      # RV wall - blue
anatomy[labels_slice == 3] = [1, 0.5, 0]    # LA wall - orange
anatomy[labels_slice == 4] = [0, 0.7, 1]    # RA wall - light blue
anatomy[labels_slice == 5] = [1, 0.7, 0.7]  # LV blood - light red
anatomy[labels_slice == 6] = [0.5, 0.5, 1]  # RV blood - lighter blue
anatomy[labels_slice == 7] = [1, 0.8, 0.5]  # LA blood - light orange
anatomy[labels_slice == 8] = [0.5, 0.85, 1] # RA blood - very light blue

plt.imshow(anatomy)
plt.title('Anatomical Labels')
plt.axis('off')

# Right panel: Simulated MRI
plt.subplot(1, 2, 2)
image_slice = weighted_images[seq_name][frame_idx][:, :, z_slice]
plt.imshow(image_slice, cmap='gray')
plt.colorbar(fraction=0.046, pad=0.04)
plt.title(f'{sequences[seq_name]["description"]}\n(TR/TE: {sequences[seq_name]["TR"]}/{sequences[seq_name]["TE"]} ms)')
plt.axis('off')

plt.suptitle(f'Frame {frame_idx + 1}: Anatomy vs Simulated MRI', fontsize=14)
plt.tight_layout()
plt.show()

# Calculate contrast-to-noise ratio (CNR) between blood and myocardium
print("\nContrast Analysis (Blood vs Myocardium):")
print("-" * 60)

for seq_name in sequences.keys():
    print(f"\n{sequences[seq_name]['description']}:")
    
    # Use frame 1 for analysis
    image = weighted_images[seq_name][0][:, :, z_slice]
    labels = frame_data[0]['Labels'][:, :, z_slice]
    
    # Extract mean signal intensities for all cardiac components
    cardiac_labels = {
        1: 'LV wall', 2: 'RV wall', 3: 'LA wall', 4: 'RA wall',
        5: 'LV blood', 6: 'RV blood', 7: 'LA blood', 8: 'RA blood'
    }
    
    # Calculate mean signals
    mean_signals = {}
    for label_id, label_name in cardiac_labels.items():
        if np.any(labels == label_id):
            mean_signals[label_name] = np.mean(image[labels == label_id])
    
    # Print key contrasts
    if 'LV blood' in mean_signals and 'LV wall' in mean_signals:
        lv_contrast = abs(mean_signals['LV blood'] - mean_signals['LV wall'])
        lv_relative = lv_contrast / mean_signals['LV wall'] * 100
        print(f"  LV Blood vs Wall: {lv_contrast:.2f} ({lv_relative:.1f}% relative)")
    
    if 'RV blood' in mean_signals and 'RV wall' in mean_signals:
        rv_contrast = abs(mean_signals['RV blood'] - mean_signals['RV wall'])
        rv_relative = rv_contrast / mean_signals['RV wall'] * 100
        print(f"  RV Blood vs Wall: {rv_contrast:.2f} ({rv_relative:.1f}% relative)")
    
    # Background signal
    bg_signal = np.mean(image[labels == 0])
    print(f"  Background signal: {bg_signal:.2f}")

print("\nSimulation completed!")

def create_interactive_oblique_viewer_3D(data_input, tissue_types, best_slice, maskLabels, Lu=200, Lv=100,theta = -45):
    """
    Interactive viewer that re-slices the 3D volume along an oblique plane.
    
    The new slicing plane is defined as follows:
      • Center at:  center = (best_slice, Y/2, X/2)  [using volume order (z,y,x)]
      • new_x axis: 45° direction in the axial (y–x) plane:
                     new_x = (0, cos45, sin45)
      • new_y axis: Along the original z-axis:
                     new_y = (1, 0, 0)
      • new_z axis: Perpendicular, computed as:
                     new_z = cross(new_x, new_y) = (0, cos45, -sin45)
    
    The 2D oblique slice is then extracted from the volume via:
         original_coord = center + u * new_x + v * new_y + offset * new_z,
    where u ∈ [–Lu/2, Lu/2], v ∈ [–Lv/2, Lv/2].
    
    The interactive slider controls offset (displacement along new_z).
    
    Parameters:
      data_input : 3D numpy array with shape (Z, Y, X)
      tissue_types : dict (unused here; for consistency)
      best_slice : int, the axial slice index to serve as center (e.g. 604)
      maskLabels : unused here
      Lu : length (in pixels) along new_x (the oblique direction in the axial plane)
      Lv : length (in pixels) along new_y (the original z direction)
    """
    import numpy as np
    import matplotlib.pyplot as plt
    from scipy import ndimage
    import ipywidgets as widgets
    from ipywidgets import interactive_output, VBox, IntSlider

    # Determine volume dimensions and center point.
    Z, Y, X = data_input.shape
    center = np.array([best_slice, Y/2, X/2])  # center = (z, y, x)
    
    # Define new coordinate directions.
    # new_x: 45° in the axial (y,x) plane, no z component.
    # theta = -45
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])  # e.g. (0, 0.7071, 0.7071)
    # new_y: along the original z-axis.
    new_y = np.array([1, 0, 0])
    # new_z: perpendicular to both.
    new_z = np.cross(new_x, new_y)  # = (0, cos45, -sin45) ≈ (0, 0.7071, -0.7071)
    
    def extract_oblique_slice(offset):
        """
        Extract a 2D oblique slice from the 3D volume.
        
        For pixel coordinates (u, v) in the new plane, mapping is given by:
           original = center + u * new_x + v * new_y + offset * new_z.
        u is in [-Lu/2, Lu/2] and v in [-Lv/2, Lv/2].
        Out-of-bound values are padded with 0.
        """
        Lu_pixels = int(Lu)
        Lv_pixels = int(Lv)
        # Create grid for new slice coordinates.
        u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
        v = np.linspace(Lv/2, -Lv/2, Lv_pixels)
        U, V = np.meshgrid(u, v)  # shape (Lv_pixels, Lu_pixels)
        # Compute original coordinates.
        # Note: Original coordinate order: (z, y, x).
        Z_coord = center[0] + V * new_y[0] + offset * new_z[0]  # new_y contributes to z; new_z[0] is 0.
        Y_coord = center[1] + U * new_x[1] + offset * new_z[1]  # new_x[1] = cos45; new_z[1] = cos45.
        X_coord = center[2] + U * new_x[2] + offset * new_z[2]  # new_x[2] = sin45; new_z[2] = -sin45.
        coords = [Z_coord, Y_coord, X_coord]
        oblique_slice = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)
        return oblique_slice

    def update(offset):
        plt.clf()
        fig, (ax_left, ax_right) = plt.subplots(1, 2, figsize=(20, 10))
        
        # Left panel: show the original axial slice (at best_slice).
        axial = data_input[best_slice, :, :]
        ax_left.imshow(axial, cmap='gray')
        ax_left.set_title(f'Axial Slice {best_slice}', fontsize=16)
        ax_left.axis('off')
        # Compute intersection (line) of the oblique plane with axial plane (z = best_slice).
        # The intersection occurs when: center + u*new_x + v*new_y + offset*new_z has z = best_slice.
        # Since center_z = best_slice and new_y = (1,0,0), we have: best_slice + v = best_slice  => v = 0.
        # Thus, for varying u with v=0:
        u_line = np.linspace(-Lu/2, Lu/2, 100)
        v_line = np.zeros_like(u_line)  # v = 0
        # Then:
        y_line = center[1] + u_line * new_x[1] + offset * new_z[1]
        x_line = center[2] + u_line * new_x[2] + offset * new_z[2]
        ax_left.plot(x_line, y_line, 'r--', linewidth=2)
        
        # Right panel: oblique slice extracted from the volume.
        obslice = extract_oblique_slice(offset)
        ax_right.imshow(obslice, cmap='inferno', vmin=np.min(obslice), vmax=np.max(obslice))
        ax_right.set_title(f'Oblique Slice (offset = {offset}px)', fontsize=16)
        ax_right.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    slider = IntSlider(min=-50, max=50, step=1, value=0, description='Offset (px):', continuous_update=False)
    ui = VBox([slider])
    out = interactive_output(update, {'offset': slider})
    display(ui, out)


# Example usage:
# Here we choose slice 604 as the reference axial slice.
param_data_input = weighted_images['T2w_SE'][0][:, :, :]
param_data_input = np.transpose(param_data_input,[2, 0, 1])
best_slice = param_data_input.shape[0] // 2  # Get the middle slice index
Lu=150
Lv=150
theta = -45
create_interactive_oblique_viewer_3D(param_data_input, None, best_slice, None, Lu, Lv,theta)

import numpy as np
from scipy import ndimage

def create_oblique_3D_volume(data_input, best_slice, Lu=200, Lv=100, Lw=100, theta=-45):
    """
    Generate a full 3D volume after oblique transformation.
    
    Parameters:
      data_input : 3D numpy array with shape (Z, Y, X)
      best_slice : int, the axial slice index to serve as center
      Lu : length (in pixels) along new_x (the oblique direction in the axial plane)
      Lv : length (in pixels) along new_y (the original z direction)
      Lw : number of slices in the output volume (along new_z direction)
      theta : angle in degrees for the oblique plane orientation
    
    Returns:
      oblique_volume : 3D numpy array representing the transformed volume
    """
    import numpy as np
    from scipy import ndimage
    
    # Determine volume dimensions and center point
    Z, Y, X = data_input.shape
    center = np.array([best_slice, Y/2, X/2])  # center = (z, y, x)
    
    # Define new coordinate directions
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])
    new_y = np.array([1, 0, 0])
    new_z = np.cross(new_x, new_y)
    
    # Create output volume
    oblique_volume = np.zeros((Lw, int(Lv), int(Lu)))
    
    # Calculate offset range to center the output volume
    offset_start = -Lw/2
    offset_end = Lw/2
    offsets = np.linspace(offset_start, offset_end, Lw)
    
    # Generate each slice of the volume
    for i, offset in enumerate(offsets):
        # Create grid for slice coordinates
        Lu_pixels = int(Lu)
        Lv_pixels = int(Lv)
        u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
        v = np.linspace(Lv/2, -Lv/2, Lv_pixels)  # Flip v-axis as in your current implementation
        U, V = np.meshgrid(u, v)
        
        # Compute original coordinates
        Z_coord = center[0] + V * new_y[0] + offset * new_z[0]
        Y_coord = center[1] + U * new_x[1] + offset * new_z[1]
        X_coord = center[2] + U * new_x[2] + offset * new_z[2]
        coords = [Z_coord, Y_coord, X_coord]
        
        # Extract the slice and add to volume
        oblique_slice = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)
        oblique_volume[i] = oblique_slice
    
    return oblique_volume
    
    
offset = -10
Lw = 100
ref_volume = create_oblique_3D_volume(param_data_input, best_slice, Lu, Lv, Lw, theta)

print("Oblique 3D volume stored in ref_volume with shape:", ref_volume.shape)
ref_slice_index =  ref_volume.shape[0] // 2  + offset# Get the middle slice index
print("Central oblique slice index:", ref_slice_index)

create_interactive_oblique_viewer_3D(ref_volume, None, ref_slice_index, None,  Lu=150, Lv=150,theta = -65)

offset2 = -5
Lw = 100
theta2 = -65

ref_volume2 = create_oblique_3D_volume(ref_volume, best_slice, Lu, Lv, Lw, theta2)
print("Oblique 3D volume stored in ref_volume with shape:", ref_volume.shape)
ref_slice_index2 =  ref_volume2.shape[0] // 2  + offset2# Get the middle slice index
print("Central oblique slice index:", ref_slice_index2)


create_interactive_oblique_viewer_3D(ref_volume2, None, ref_slice_index2, None,  Lu=150, Lv=150, theta=0)
theta3 = 0
ref_volume3 = create_oblique_3D_volume(ref_volume2, best_slice, Lu, Lv, Lw, theta3)

import numpy as np
import matplotlib.pyplot as plt
from scipy import ndimage
import ipywidgets as widgets
from ipywidgets import interactive_output, VBox, IntSlider

def create_oblique_3D_volume(data_input, best_slice, Lu=200, Lv=100, Lw=100, theta=-45):
    """
    Generate a full 3D volume after oblique transformation.
    
    Parameters:
      data_input : 3D numpy array with shape (Z, Y, X)
      best_slice : int, the axial slice index to serve as center
      Lu : length (in pixels) along new_x (the oblique direction in the axial plane)
      Lv : length (in pixels) along new_y (the original z direction)
      Lw : number of slices in the output volume (along new_z direction)
      theta : angle in degrees for the oblique plane orientation
    
    Returns:
      oblique_volume : 3D numpy array representing the transformed volume
    """
    # Determine volume dimensions and center point
    Z, Y, X = data_input.shape
    center = np.array([best_slice, Y/2, X/2])  # center = (z, y, x)
    
    # Define new coordinate directions
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])
    new_y = np.array([1, 0, 0])
    new_z = np.cross(new_x, new_y)
    
    # Create output volume
    oblique_volume = np.zeros((Lw, int(Lv), int(Lu)))
    
    # Calculate offset range to center the output volume
    offset_start = -Lw/2
    offset_end = Lw/2
    offsets = np.linspace(offset_start, offset_end, Lw)
    
    # Generate each slice of the volume
    for i, offset in enumerate(offsets):
        # Create grid for slice coordinates
        Lu_pixels = int(Lu)
        Lv_pixels = int(Lv)
        u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
        v = np.linspace(Lv/2, -Lv/2, Lv_pixels)  # Flip v-axis
        U, V = np.meshgrid(u, v)
        
        # Compute original coordinates
        Z_coord = center[0] + V * new_y[0] + offset * new_z[0]
        Y_coord = center[1] + U * new_x[1] + offset * new_z[1]
        X_coord = center[2] + U * new_x[2] + offset * new_z[2]
        coords = [Z_coord, Y_coord, X_coord]
        
        # Extract the slice and add to volume
        oblique_slice = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)
        oblique_volume[i] = oblique_slice
    
    return oblique_volume

def create_interactive_oblique_viewer_3D(data_input, tissue_types, best_slice, maskLabels, Lu=200, Lv=100, theta=-45):
    """
    Interactive viewer that re-slices the 3D volume along an oblique plane.
    """
    # Determine volume dimensions and center point
    Z, Y, X = data_input.shape
    center = np.array([best_slice, Y/2, X/2])  # center = (z, y, x)
    
    # Define new coordinate directions
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])
    new_y = np.array([1, 0, 0])
    new_z = np.cross(new_x, new_y)
    
    def extract_oblique_slice(offset):
        """Extract a 2D oblique slice from the 3D volume."""
        Lu_pixels = int(Lu)
        Lv_pixels = int(Lv)
        u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
        v = np.linspace(Lv/2, -Lv/2, Lv_pixels)
        U, V = np.meshgrid(u, v)
        
        Z_coord = center[0] + V * new_y[0] + offset * new_z[0]
        Y_coord = center[1] + U * new_x[1] + offset * new_z[1]
        X_coord = center[2] + U * new_x[2] + offset * new_z[2]
        coords = [Z_coord, Y_coord, X_coord]
        
        oblique_slice = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)
        return oblique_slice

    def update(offset):
        plt.clf()
        fig, (ax_left, ax_right) = plt.subplots(1, 2, figsize=(20, 10))
        
        # Left panel: show the original axial slice
        axial = data_input[best_slice, :, :]
        ax_left.imshow(axial, cmap='gray')
        ax_left.set_title(f'Reference Slice {best_slice}', fontsize=16)
        ax_left.axis('off')
        
        # Compute intersection line
        u_line = np.linspace(-Lu/2, Lu/2, 100)
        v_line = np.zeros_like(u_line)
        y_line = center[1] + u_line * new_x[1] + offset * new_z[1]
        x_line = center[2] + u_line * new_x[2] + offset * new_z[2]
        ax_left.plot(x_line, y_line, 'r--', linewidth=2)
        
        # Right panel: oblique slice
        obslice = extract_oblique_slice(offset)
        ax_right.imshow(obslice, cmap='gray', vmin=np.min(obslice), vmax=np.max(obslice))
        ax_right.set_title(f'Oblique Slice (offset = {offset}px, theta = {theta}°)', fontsize=16)
        ax_right.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    slider = IntSlider(min=-50, max=50, step=1, value=0, description='Offset (px):', continuous_update=False)
    ui = VBox([slider])
    out = interactive_output(update, {'offset': slider})
    display(ui, out)

# Transform all modalities and time frames to short-axis view
print("Transforming all modalities and time frames to short-axis view...")
print("=" * 70)

# Parameters for transformations
Lu = 150
Lv = 150
Lw = 100

# Transformation parameters for the three steps
transformations = [
    {'name': 'Vertical Long Axis (VLA)', 'theta': -45, 'offset': -10},
    {'name': 'Horizontal Long Axis (HLA)', 'theta': -65, 'offset': -5},
    {'name': 'Short Axis (SA)', 'theta': 0, 'offset': 0}
]

# Store short-axis volumes for all sequences and frames
short_axis_volumes = {}

for seq_name in sequences.keys():
    print(f"\nProcessing {sequences[seq_name]['description']}...")
    short_axis_volumes[seq_name] = []
    
    for frame_idx in range(n_frames):
        print(f"  Frame {frame_idx + 1}:")
        
        # Get the weighted image and transpose to (Z, Y, X) order
        volume = weighted_images[seq_name][frame_idx]
        volume = np.transpose(volume, [2, 0, 1])  # From (Y, X, Z) to (Z, Y, X)
        
        # Apply the three transformations sequentially
        current_volume = volume
        best_slice = volume.shape[0] // 2  # Initial middle slice
        
        for i, transform in enumerate(transformations):
            if i > 0:  # Update best_slice for subsequent transformations
                best_slice = current_volume.shape[0] // 2 + transformations[i-1]['offset']
            
            current_volume = create_oblique_3D_volume(
                current_volume, 
                best_slice, 
                Lu=Lu, 
                Lv=Lv, 
                Lw=Lw, 
                theta=transform['theta']
            )
            print(f"    {transform['name']}: shape {current_volume.shape}")
        
        # Store the final short-axis volume
        short_axis_volumes[seq_name].append(current_volume)

print("\nShort-axis transformation complete!")

# Visualize short-axis views for all sequences (Frame 1)
frame_to_show = 0
sa_slice_idx = Lw // 2  - 10# Middle slice of short-axis volume

plt.figure(figsize=(18, 12))
plt.suptitle(f'Short-Axis Views - Frame {frame_to_show + 1}, Slice {sa_slice_idx}', fontsize=16)

# Show original axial views (top row) and short-axis views (bottom row)
for i, (seq_name, seq_params) in enumerate(sequences.items()):
    # Original axial view
    plt.subplot(2, len(sequences), i + 1)
    axial_slice = weighted_images[seq_name][frame_to_show][:, :, z_slice]
    plt.imshow(axial_slice, cmap='gray')
    plt.title(f'{seq_params["description"]}\n(Axial)', fontsize=10)
    plt.axis('off')
    
    # Short-axis view
    plt.subplot(2, len(sequences), i + 1 + len(sequences))
    sa_slice = short_axis_volumes[seq_name][frame_to_show][sa_slice_idx, :, :]
    plt.imshow(sa_slice, cmap='gray')
    plt.title(f'{seq_params["description"]}\n(Short-Axis)', fontsize=10)
    plt.axis('off')

plt.tight_layout()
plt.show()

# Create animated cine display for short-axis T2w SE
seq_to_animate = 'T2w_SE'
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
fig.suptitle(f'{sequences[seq_to_animate]["description"]} - Axial vs Short-Axis Cine', fontsize=16)

# Find consistent display ranges
axial_images = [weighted_images[seq_to_animate][i][:, :, z_slice] for i in range(n_frames)]
sa_images = [short_axis_volumes[seq_to_animate][i][sa_slice_idx, :, :] for i in range(n_frames)]

axial_vmin = min(np.percentile(img, 1) for img in axial_images)
axial_vmax = max(np.percentile(img, 99) for img in axial_images)
sa_vmin = min(np.percentile(img, 1) for img in sa_images)
sa_vmax = max(np.percentile(img, 99) for img in sa_images)

# Initial images
im1 = ax1.imshow(axial_images[0], cmap='gray', vmin=axial_vmin, vmax=axial_vmax)
im2 = ax2.imshow(sa_images[0], cmap='gray', vmin=sa_vmin, vmax=sa_vmax)

ax1.set_title('Axial View')
ax2.set_title('Short-Axis View')
ax1.axis('off')
ax2.axis('off')

frame_text = fig.text(0.5, 0.02, '', ha='center', fontsize=12)

def animate_comparison(frame):
    im1.set_array(axial_images[frame])
    im2.set_array(sa_images[frame])
    frame_text.set_text(f'Frame {frame + 1}/{n_frames}')
    return [im1, im2, frame_text]

anim_comparison = FuncAnimation(fig, animate_comparison, frames=n_frames, 
                                interval=200, blit=True, repeat=True)
plt.show()

# Interactive viewer for exploring short-axis volume
# print("\nInteractive Short-Axis Viewer for T2w SE, Frame 1:")
# sa_volume = short_axis_volumes['T2w_SE'][0]
# sa_best_slice = sa_volume.shape[0] // 2
# create_interactive_oblique_viewer_3D(sa_volume, None, sa_best_slice, None, Lu=150, Lv=150, theta=0)

# Compare cardiac structure visibility in axial vs short-axis
print("\nCardiac Structure Analysis - Axial vs Short-Axis:")
print("-" * 60)

# Transform the label volume to short-axis as well
label_volume = frame_data[0]['Labels']
label_volume_transposed = np.transpose(label_volume, [2, 0, 1])
current_label_volume = label_volume_transposed
best_slice = label_volume_transposed.shape[0] // 2

# Apply same transformations to labels
for i, transform in enumerate(transformations):
    if i > 0:
        best_slice = current_label_volume.shape[0] // 2 + transformations[i-1]['offset']
    
    current_label_volume = create_oblique_3D_volume(
        current_label_volume, 
        best_slice, 
        Lu=Lu, 
        Lv=Lv, 
        Lw=Lw, 
        theta=transform['theta']
    )

# Analyze structure visibility
axial_labels = frame_data[0]['Labels'][:, :, z_slice]
sa_labels = current_label_volume[sa_slice_idx, :, :]

cardiac_labels = {
    1: 'LV wall', 2: 'RV wall', 3: 'LA wall', 4: 'RA wall',
    5: 'LV blood', 6: 'RV blood', 7: 'LA blood', 8: 'RA blood'
}

print("\nStructure visibility (pixel count):")
for label_id, label_name in cardiac_labels.items():
    axial_count = np.sum(axial_labels == label_id)
    sa_count = np.sum(sa_labels == label_id)
    print(f"{label_name:12} - Axial: {axial_count:5d}, Short-axis: {sa_count:5d}")

# Visualize label comparison
plt.figure(figsize=(12, 6))

plt.subplot(1, 2, 1)
# Create color-coded anatomy for axial
anatomy_axial = np.zeros((*axial_labels.shape, 3))
anatomy_axial[axial_labels == 1] = [1, 0, 0]      # LV wall - red
anatomy_axial[axial_labels == 2] = [0, 0, 1]      # RV wall - blue
anatomy_axial[axial_labels == 3] = [1, 0.5, 0]    # LA wall - orange
anatomy_axial[axial_labels == 4] = [0, 0.7, 1]    # RA wall - light blue
anatomy_axial[axial_labels == 5] = [1, 0.7, 0.7]  # LV blood - light red
anatomy_axial[axial_labels == 6] = [0.5, 0.5, 1]  # RV blood - lighter blue
anatomy_axial[axial_labels == 7] = [1, 0.8, 0.5]  # LA blood - light orange
anatomy_axial[axial_labels == 8] = [0.5, 0.85, 1] # RA blood - very light blue
plt.imshow(anatomy_axial)
plt.title('Axial View - Cardiac Anatomy')
plt.axis('off')

plt.subplot(1, 2, 2)
# Create color-coded anatomy for short-axis
anatomy_sa = np.zeros((*sa_labels.shape, 3))
anatomy_sa[sa_labels == 1] = [1, 0, 0]      # LV wall - red
anatomy_sa[sa_labels == 2] = [0, 0, 1]      # RV wall - blue
anatomy_sa[sa_labels == 3] = [1, 0.5, 0]    # LA wall - orange
anatomy_sa[sa_labels == 4] = [0, 0.7, 1]    # RA wall - light blue
anatomy_sa[sa_labels == 5] = [1, 0.7, 0.7]  # LV blood - light red
anatomy_sa[sa_labels == 6] = [0.5, 0.5, 1]  # RV blood - lighter blue
anatomy_sa[sa_labels == 7] = [1, 0.8, 0.5]  # LA blood - light orange
anatomy_sa[sa_labels == 8] = [0.5, 0.85, 1] # RA blood - very light blue
plt.imshow(anatomy_sa)
plt.title('Short-Axis View - Cardiac Anatomy')
plt.axis('off')

plt.tight_layout()
plt.show()

print("\nShort-axis transformation pipeline complete!")
print("All sequences and time frames have been transformed to short-axis view.")

# import numpy as np
# import matplotlib.pyplot as plt
# from scipy import ndimage
# from sklearn.decomposition import PCA
# from scipy.spatial.transform import Rotation

# def find_lv_axes_pca(label_volume, lv_labels=[1, 5]):
#     """
#     Find the principal axes of the left ventricle using PCA.
    
#     Parameters:
#     -----------
#     label_volume : 3D array
#         Segmentation volume with labeled cardiac structures
#     lv_labels : list
#         Label values for LV structures (default=[1, 5] for wall and blood)
    
#     Returns:
#     --------
#     centroid : array
#         Center of mass of LV
#     axes : array (3x3)
#         Principal axes as column vectors [v1, v2, v3]
#         v1: long axis (largest eigenvalue)
#         v2: second principal axis
#         v3: third principal axis
#     eigenvalues : array
#         Eigenvalues corresponding to each axis
#     """
#     # Find all LV voxel coordinates (both wall and blood)
#     lv_mask = np.zeros_like(label_volume, dtype=bool)
#     for label in lv_labels:
#         lv_mask |= (label_volume == label)
    
#     lv_coords = np.argwhere(lv_mask)
    
#     if len(lv_coords) == 0:
#         raise ValueError(f"No voxels found with labels {lv_labels}")
    
#     print(f"Found {len(lv_coords)} LV voxels (wall + blood)")
    
#     # Calculate centroid
#     centroid = np.mean(lv_coords, axis=0)
    
#     # Center the coordinates
#     centered_coords = lv_coords - centroid
    
#     # Perform PCA
#     pca = PCA(n_components=3)
#     pca.fit(centered_coords)
    
#     # Get principal axes (eigenvectors) and eigenvalues
#     axes = pca.components_.T  # Transpose to get column vectors
#     eigenvalues = pca.explained_variance_
    
#     # Ensure right-handed coordinate system
#     if np.linalg.det(axes) < 0:
#         axes[:, 2] = -axes[:, 2]
    
#     return centroid, axes, eigenvalues

# def create_rotation_matrix(long_axis):
#     """
#     Create a rotation matrix that aligns the long axis with the z-axis.
    
#     Parameters:
#     -----------
#     long_axis : array
#         The long axis vector (will be normalized)
    
#     Returns:
#     --------
#     R : array (3x3)
#         Rotation matrix
#     """
#     # Normalize long axis
#     v1 = long_axis / np.linalg.norm(long_axis)
    
#     # Find two perpendicular vectors to form an orthonormal basis
#     # Choose an arbitrary vector not parallel to v1
#     if abs(v1[0]) < 0.9:
#         arbitrary = np.array([1, 0, 0])
#     else:
#         arbitrary = np.array([0, 1, 0])
    
#     # Create orthogonal vector using cross product
#     v2 = np.cross(v1, arbitrary)
#     v2 = v2 / np.linalg.norm(v2)
    
#     # Create third orthogonal vector
#     v3 = np.cross(v1, v2)
#     v3 = v3 / np.linalg.norm(v3)
    
#     # Rotation matrix: columns are the new basis vectors
#     # We want v1 to align with z-axis, so it goes in the third column
#     R = np.column_stack([v3, v2, v1])
    
#     return R

# def reorient_volume_to_short_axis(volume, label_volume, lv_labels=[1, 5], output_shape=None):
#     """
#     Reorient a volume so that short-axis slices are aligned with the image grid.
    
#     Parameters:
#     -----------
#     volume : 3D array
#         Volume to reorient (e.g., MRI data)
#     label_volume : 3D array
#         Segmentation volume for finding LV axes
#     lv_labels : list
#         Label values for LV structures (wall and blood)
#     output_shape : tuple
#         Desired output shape (default: same as input)
    
#     Returns:
#     --------
#     reoriented_volume : 3D array
#         Volume reoriented to short-axis view
#     transform_info : dict
#         Information about the transformation applied
#     """
#     if output_shape is None:
#         output_shape = volume.shape
    
#     # Find LV axes using PCA
#     centroid, axes, eigenvalues = find_lv_axes_pca(label_volume, lv_labels)
    
#     # The long axis is the first principal component (largest eigenvalue)
#     long_axis = axes[:, 0]
    
#     print(f"LV centroid: {centroid}")
#     print(f"Long axis direction: {long_axis}")
#     print(f"Eigenvalue ratios: {eigenvalues / eigenvalues[0]}")
    
#     # Create rotation matrix to align long axis with z-axis
#     R = create_rotation_matrix(long_axis)
    
#     # Create coordinate grids for the output volume
#     z_out, y_out, x_out = np.meshgrid(
#         np.arange(output_shape[0]) - output_shape[0]/2,
#         np.arange(output_shape[1]) - output_shape[1]/2,
#         np.arange(output_shape[2]) - output_shape[2]/2,
#         indexing='ij'
#     )
    
#     # Stack coordinates
#     coords_out = np.stack([z_out, y_out, x_out], axis=-1)
    
#     # Apply inverse rotation to find source coordinates
#     coords_out_flat = coords_out.reshape(-1, 3)
#     coords_src_flat = coords_out_flat @ R + centroid
    
#     # Reshape back
#     coords_src = coords_src_flat.reshape(*output_shape, 3)
    
#     # Separate coordinates
#     z_src = coords_src[..., 0]
#     y_src = coords_src[..., 1]
#     x_src = coords_src[..., 2]
    
#     # Interpolate
#     reoriented_volume = ndimage.map_coordinates(
#         volume, 
#         [z_src, y_src, x_src], 
#         order=1, 
#         mode='constant', 
#         cval=0
#     )
    
#     transform_info = {
#         'centroid': centroid,
#         'axes': axes,
#         'eigenvalues': eigenvalues,
#         'long_axis': long_axis,
#         'rotation_matrix': R
#     }
    
#     return reoriented_volume, transform_info

# # Process all modalities and time frames with automatic short-axis finding
# print("Automatic Short-Axis View Transformation")
# print("=" * 70)

# # Define which labels to use for axis finding
# # CHANGE THIS TO TEST DIFFERENT STRUCTURES!
# # Uncomment the line you want to use:

# # axis_labels = [1, 5]      # LV wall and blood (default)
# # axis_labels = [1, 3, 5]      # LV wall and blood LA wall
# # axis_labels = [3]       # LA wall only
# # axis_labels = [3, 7]    # LA wall and blood
# # axis_labels = [2, 6]    # RV wall and blood
# # axis_labels = [4, 8]    # RA wall and blood
# axis_labels = [1]       # LV wall only
# # axis_labels = [5]       # LV blood only

# print(f"Using labels {axis_labels} for axis detection")
# print("Available labels:")
# print("  1: LV wall,  5: LV blood")
# print("  2: RV wall,  6: RV blood")
# print("  3: LA wall,  7: LA blood")
# print("  4: RA wall,  8: RA blood")
# print("=" * 70)

# # Store automatically aligned short-axis volumes
# auto_short_axis_volumes = {}
# transform_infos = {}

# # Use the first frame's labels to determine the cardiac orientation
# # (assuming cardiac orientation doesn't change significantly between frames)
# reference_labels = frame_data[0]['Labels']

# # Find the reference transformation using the first frame
# print("Finding cardiac axes from reference frame...")
# _, ref_transform_info = reorient_volume_to_short_axis(
#     reference_labels, 
#     reference_labels, 
#     lv_labels=axis_labels  # Use the defined labels
# )

# print(f"\nReference long axis: {ref_transform_info['long_axis']}")
# print(f"Eigenvalue ratios: {ref_transform_info['eigenvalues'] / ref_transform_info['eigenvalues'][0]}")

# # Process each sequence
# for seq_name in sequences.keys():
#     print(f"\nProcessing {sequences[seq_name]['description']}...")
#     auto_short_axis_volumes[seq_name] = []
#     transform_infos[seq_name] = []
    
#     for frame_idx in range(n_frames):
#         # Get the weighted image
#         volume = weighted_images[seq_name][frame_idx]
        
#         # Get the corresponding label volume
#         label_vol = frame_data[frame_idx]['Labels']
        
#         # Apply automatic short-axis transformation
#         sa_volume, transform_info = reorient_volume_to_short_axis(
#             volume, 
#             label_vol,
#             lv_labels=axis_labels,  # Use the same defined labels
#             output_shape=(100, 150, 150)  # Adjust as needed
#         )
        
#         auto_short_axis_volumes[seq_name].append(sa_volume)
#         transform_infos[seq_name].append(transform_info)
        
#         print(f"  Frame {frame_idx + 1}: shape {sa_volume.shape}")

# # Update the label names based on what we're analyzing
# if axis_labels == [1, 5]:
#     structure_name = "LV (wall + blood)"
# elif axis_labels == [1]:
#     structure_name = "LV wall"
# elif axis_labels == [5]:
#     structure_name = "LV blood"
# elif axis_labels == [3]:
#     structure_name = "LA wall"
# elif axis_labels == [3, 7]:
#     structure_name = "LA (wall + blood)"
# elif axis_labels == [2, 6]:
#     structure_name = "RV (wall + blood)"
# elif axis_labels == [2]:
#     structure_name = "RV wall"
# elif axis_labels == [6]:
#     structure_name = "RV blood"
# elif axis_labels == [4, 8]:
#     structure_name = "RA (wall + blood)"
# elif axis_labels == [4]:
#     structure_name = "RA wall"
# elif axis_labels == [8]:
#     structure_name = "RA blood"
# else:
#     structure_name = f"Labels {axis_labels}"

# print(f"\nAutomatic short-axis transformation complete using {structure_name}!")

# # Visualize the results
# fig = plt.figure(figsize=(20, 15))
# fig.suptitle('Automatic vs Manual Short-Axis View Comparison', fontsize=16)

# # Select middle slices for comparison
# auto_sa_slice_idx = auto_short_axis_volumes[list(sequences.keys())[0]][0].shape[0] // 2
# manual_sa_slice_idx = short_axis_volumes[list(sequences.keys())[0]][0].shape[0] // 2

# seq_to_show = 'T2w_SE'
# frame_to_show = 0

# # Row 1: Original axial view
# ax1 = plt.subplot(3, 3, 1)
# axial_slice = weighted_images[seq_to_show][frame_to_show][:, :, z_slice]
# ax1.imshow(axial_slice, cmap='gray')
# ax1.set_title('Original Axial View')
# ax1.axis('off')

# # Row 1: Manual short-axis
# ax2 = plt.subplot(3, 3, 2)
# manual_sa = short_axis_volumes[seq_to_show][frame_to_show][manual_sa_slice_idx, :, :]
# ax2.imshow(manual_sa, cmap='gray')
# ax2.set_title('Manual Short-Axis\n(Fixed Parameters)')
# ax2.axis('off')

# # Row 1: Automatic short-axis
# ax3 = plt.subplot(3, 3, 3)
# auto_sa = auto_short_axis_volumes[seq_to_show][frame_to_show][auto_sa_slice_idx, :, :]
# ax3.imshow(auto_sa, cmap='gray')
# ax3.set_title('Automatic Short-Axis\n(PCA-based)')
# ax3.axis('off')

# # Row 2: Show multiple slices from automatic short-axis
# for i in range(3):
#     ax = plt.subplot(3, 3, 4 + i)
#     slice_offset = (i - 1) * 10  # Show slices at different positions
#     slice_idx = auto_sa_slice_idx + slice_offset
#     if 0 <= slice_idx < auto_short_axis_volumes[seq_to_show][frame_to_show].shape[0]:
#         sa_slice = auto_short_axis_volumes[seq_to_show][frame_to_show][slice_idx, :, :]
#         ax.imshow(sa_slice, cmap='gray')
#         ax.set_title(f'Auto SA Slice {slice_idx}')
#     ax.axis('off')

# # Row 3: Show all modalities with automatic short-axis
# for i, (seq_name, seq_params) in enumerate(sequences.items()):
#     ax = plt.subplot(3, 3, 7 + i)
#     auto_sa_slice = auto_short_axis_volumes[seq_name][frame_to_show][auto_sa_slice_idx, :, :]
#     ax.imshow(auto_sa_slice, cmap='gray')
#     ax.set_title(f'{seq_params["description"]}\n(Auto Short-Axis)')
#     ax.axis('off')

# plt.tight_layout()
# plt.show()

# # Visualize the PCA results and cutting planes
# fig2 = plt.figure(figsize=(18, 12))
# fig2.suptitle(f'{structure_name} Principal Component Analysis and Cutting Plane Comparison', fontsize=16)

# # Get label data for visualization
# labels = frame_data[0]['Labels']

# # Calculate angle between manual and automatic methods FIRST
# # This needs to be done before we use it in the subplots
# manual_angles = np.array([-45, -65, 0]) * np.pi / 180
# approx_manual_normal = np.array([
#     np.sin(manual_angles[0]) * np.cos(manual_angles[1]),
#     np.cos(manual_angles[0]) * np.cos(manual_angles[1]),
#     np.sin(manual_angles[1])
# ])
# approx_manual_normal = approx_manual_normal / np.linalg.norm(approx_manual_normal)

# # Calculate angle between manual and automatic long axes
# auto_long_axis = ref_transform_info['long_axis']
# angle_between = np.arccos(np.clip(np.dot(approx_manual_normal, auto_long_axis), -1, 1))
# angle_degrees = angle_between * 180 / np.pi
# angle_degrees_global = angle_degrees  # Store for later use

# # Get coordinates for visualization based on current axis_labels
# lv_mask = np.zeros_like(labels, dtype=bool)
# for label in axis_labels:
#     lv_mask |= (labels == label)
# lv_coords = np.argwhere(lv_mask)
# lv_mask_3d = lv_mask  # Store for later use
# lv_coords_3d = lv_coords  # Store for later use

# # Subplot 1: 3D scatter plot of structure
# ax1 = fig2.add_subplot(2, 3, 1, projection='3d')
# sample_indices = np.random.choice(len(lv_coords), min(2000, len(lv_coords)), replace=False)
# sampled_coords = lv_coords[sample_indices]

# # Color by tissue type
# colors = []
# for coord in sampled_coords:
#     label_val = labels[tuple(coord)]
#     if label_val in [1, 3]:  # Wall tissues
#         colors.append('darkred')
#     elif label_val in [2, 4]:  # Other wall tissues
#         colors.append('darkblue')
#     else:  # Blood pools
#         colors.append('red')

# ax1.scatter(sampled_coords[:, 2], sampled_coords[:, 1], sampled_coords[:, 0], 
#            c=colors, s=1, alpha=0.5)

# # Plot principal axes
# centroid = ref_transform_info['centroid']
# axes = ref_transform_info['axes']
# eigenvalues = ref_transform_info['eigenvalues']

# # Scale axes by eigenvalues for visualization
# scale = 30  # Adjust for visibility
# axis_colors = ['blue', 'green', 'cyan']
# labels_axes = ['Long Axis (PC1)', 'PC2', 'PC3']

# for i in range(3):
#     axis_vec = axes[:, i] * np.sqrt(eigenvalues[i]) / np.sqrt(eigenvalues[0]) * scale
#     ax1.plot([centroid[2], centroid[2] + axis_vec[2]], 
#             [centroid[1], centroid[1] + axis_vec[1]], 
#             [centroid[0], centroid[0] + axis_vec[0]], 
#             axis_colors[i], linewidth=3, label=labels_axes[i])

# ax1.set_xlabel('X')
# ax1.set_ylabel('Y')
# ax1.set_zlabel('Z')
# ax1.set_title(f'{structure_name} with Principal Axes')
# ax1.legend()

# # Subplot 2: Show cutting plane on axial view with mask
# ax2 = plt.subplot(2, 3, 2)
# axial_slice = weighted_images[seq_to_show][frame_to_show][:, :, z_slice]
# axial_labels = frame_data[frame_to_show]['Labels'][:, :, z_slice]

# # Create overlay image
# overlay = axial_slice.copy()
# overlay = (overlay - np.min(overlay)) / (np.max(overlay) - np.min(overlay))  # Normalize

# # Create RGB version for overlay
# overlay_rgb = np.stack([overlay, overlay, overlay], axis=-1)

# # Add mask overlay for selected labels
# for label_val in axis_labels:
#     mask = (axial_labels == label_val)
#     if label_val in [1, 2, 3, 4]:  # Wall tissues
#         color_intensity = 0.3
#     else:  # Blood pools
#         color_intensity = 0.6
    
#     if label_val in [1, 5]:  # LV
#         overlay_rgb[mask] = [1, color_intensity, color_intensity]  # Red tones
#     elif label_val in [2, 6]:  # RV
#         overlay_rgb[mask] = [color_intensity, color_intensity, 1]  # Blue tones
#     elif label_val in [3, 7]:  # LA
#         overlay_rgb[mask] = [1, color_intensity, 0]  # Orange tones
#     elif label_val in [4, 8]:  # RA
#         overlay_rgb[mask] = [0, color_intensity, 1]  # Cyan tones

# ax2.imshow(overlay_rgb)

# # Calculate centroid in this slice
# structure_mask = np.zeros_like(axial_labels, dtype=bool)
# for label in axis_labels:
#     structure_mask |= (axial_labels == label)

# structure_coords_slice = np.argwhere(structure_mask)
# if len(structure_coords_slice) > 0:
#     center_slice = np.mean(structure_coords_slice, axis=0)
#     ax2.plot(center_slice[1], center_slice[0], 'yo', markersize=10, label=f'{structure_name} center')

# # Show the long axis projection and cutting plane
# long_axis = ref_transform_info['long_axis']
# centroid_3d = ref_transform_info['centroid']

# # Project the 3D centroid to this axial slice
# centroid_y = centroid_3d[1]
# centroid_x = centroid_3d[2]
# ax2.plot(centroid_x, centroid_y, 'go', markersize=10, label='3D centroid')

# # Show long axis direction projected onto axial plane
# if abs(long_axis[2]) < 0.99:  # If not perfectly vertical
#     # Project long axis onto axial plane
#     projected = long_axis.copy()
#     projected[2] = 0  # Remove z component
#     if np.linalg.norm(projected) > 0:
#         projected = projected / np.linalg.norm(projected)
        
#         # Draw arrow showing long axis projection
#         arrow_length = 50
#         ax2.arrow(centroid_x, centroid_y, 
#                  projected[1] * arrow_length, projected[0] * arrow_length,
#                  head_width=8, head_length=6, fc='blue', ec='blue', 
#                  linewidth=3, alpha=0.7, label='Long axis projection')
        
#         # Show perpendicular direction (short axis direction in plane)
#         perp = np.array([-projected[1], projected[0]])
#         ax2.arrow(centroid_x, centroid_y,
#                  perp[0] * arrow_length * 0.7, perp[1] * arrow_length * 0.7,
#                  head_width=6, head_length=4, fc='cyan', ec='cyan', 
#                  linewidth=2, alpha=0.7, label='Short axis direction')
        
#         # Draw the cutting line (perpendicular to long axis projection)
#         line_length = 100
#         x_line = np.array([centroid_x - perp[0]*line_length, 
#                           centroid_x + perp[0]*line_length])
#         y_line = np.array([centroid_y - perp[1]*line_length, 
#                           centroid_y + perp[1]*line_length])
#         ax2.plot(x_line, y_line, 'c--', linewidth=2, alpha=0.7, label='SA cutting plane')

# ax2.set_title(f'Axial View (slice {z_slice}) with {structure_name} mask and axes')
# ax2.legend(loc='upper right', fontsize=8)
# ax2.axis('off')

# # Subplot 3: Eigenvalue analysis
# ax3 = plt.subplot(2, 3, 3)
# eigenvalue_ratios = eigenvalues / eigenvalues[0]
# bars = ax3.bar(range(3), eigenvalue_ratios)
# bars[0].set_color('blue')
# bars[1].set_color('green')
# bars[2].set_color('cyan')
# ax3.set_xlabel('Principal Component')
# ax3.set_ylabel('Normalized Eigenvalue')
# ax3.set_title('Eigenvalue Ratios')
# ax3.set_xticks(range(3))
# ax3.set_xticklabels(['PC1 (Long)', 'PC2', 'PC3'])
# ax3.set_ylim(0, 1.1)

# # Add text with ratios
# for i, ratio in enumerate(eigenvalue_ratios):
#     ax3.text(i, ratio + 0.02, f'{ratio:.3f}', ha='center')

# # Subplot 4: Compare manual vs automatic orientations
# ax4 = plt.subplot(2, 3, 4)

# # Display the comparison (variables already calculated above)
# ax4.text(0.1, 0.9, 'Orientation Comparison:', fontsize=14, weight='bold')
# ax4.text(0.1, 0.7, f'Automatic long axis: [{auto_long_axis[0]:.3f}, {auto_long_axis[1]:.3f}, {auto_long_axis[2]:.3f}]', fontsize=11)
# ax4.text(0.1, 0.6, f'Manual normal (approx): [{approx_manual_normal[0]:.3f}, {approx_manual_normal[1]:.3f}, {approx_manual_normal[2]:.3f}]', fontsize=11)
# ax4.text(0.1, 0.4, f'Angle between orientations: {angle_degrees:.1f}°', fontsize=12, color='red')
# ax4.text(0.1, 0.2, f'λ₁/λ₂ = {eigenvalues[0]/eigenvalues[1]:.2f} (elongation ratio)', fontsize=11)
# ax4.text(0.1, 0.1, f'λ₁/λ₃ = {eigenvalues[0]/eigenvalues[2]:.2f}', fontsize=11)
# ax4.axis('off')

# # Subplot 5: Show multiple automatic short-axis slices
# ax5 = plt.subplot(2, 3, 5)
# # Create montage of slices
# n_slices = 5
# slice_spacing = auto_short_axis_volumes[seq_to_show][frame_to_show].shape[0] // (n_slices + 1)
# montage = []
# for i in range(n_slices):
#     slice_idx = (i + 1) * slice_spacing
#     sa_slice = auto_short_axis_volumes[seq_to_show][frame_to_show][slice_idx, :, :]
#     montage.append(sa_slice)

# # Create a single image from slices
# montage_img = np.hstack(montage)
# ax5.imshow(montage_img, cmap='gray')
# ax5.set_title(f'Auto Short-Axis Slices (every {slice_spacing} slices)')
# ax5.axis('off')

# # Subplot 6: Difference map
# ax6 = plt.subplot(2, 3, 6)
# # Compare similar slices from manual and auto
# manual_sa = short_axis_volumes[seq_to_show][frame_to_show][manual_sa_slice_idx, :, :]
# auto_sa = auto_short_axis_volumes[seq_to_show][frame_to_show][auto_sa_slice_idx, :, :]

# # Resize to same shape if needed
# if manual_sa.shape != auto_sa.shape:
#     from scipy.ndimage import zoom
#     zoom_factors = np.array(auto_sa.shape) / np.array(manual_sa.shape)
#     manual_sa_resized = zoom(manual_sa, zoom_factors, order=1)
# else:
#     manual_sa_resized = manual_sa

# # Calculate normalized difference
# max_val = max(np.max(manual_sa_resized), np.max(auto_sa))
# if max_val > 0:
#     diff = np.abs(auto_sa - manual_sa_resized) / max_val
# else:
#     diff = np.abs(auto_sa - manual_sa_resized)

# im = ax6.imshow(diff, cmap='hot', vmin=0, vmax=0.5)
# ax6.set_title('Normalized Difference\n(Auto - Manual)')
# ax6.axis('off')
# plt.colorbar(im, ax=ax6, fraction=0.046, pad=0.04)

# plt.tight_layout()
# plt.show()

# # Additional analysis: Show how well the structure is aligned
# # Get the automatically reoriented label volume
# reoriented_labels, _ = reorient_volume_to_short_axis(
#     frame_data[0]['Labels'],
#     frame_data[0]['Labels'],
#     lv_labels=axis_labels,  # Use the same axis_labels
#     output_shape=(100, 150, 150)
# )

# fig3 = plt.figure(figsize=(15, 10))
# fig3.suptitle(f'Validation of Automatic Short-Axis Alignment for {structure_name}', fontsize=16)

# # Show several short-axis slices with labels
# n_rows = 2
# n_cols = 3
# for i in range(n_rows * n_cols):
#     ax = plt.subplot(n_rows, n_cols, i + 1)
    
#     # Select slices from different positions along the long axis
#     slice_idx = int((i + 1) * reoriented_labels.shape[0] / (n_rows * n_cols + 1))
#     label_slice = reoriented_labels[slice_idx, :, :]
    
#     # Create color-coded anatomy
#     anatomy = np.zeros((*label_slice.shape, 3))
#     anatomy[label_slice == 1] = [1, 0, 0]      # LV wall - red
#     anatomy[label_slice == 2] = [0, 0, 1]      # RV wall - blue
#     anatomy[label_slice == 3] = [1, 0.5, 0]    # LA wall - orange
#     anatomy[label_slice == 4] = [0, 0.7, 1]    # RA wall - light blue
#     anatomy[label_slice == 5] = [1, 0.7, 0.7]  # LV blood - light red
#     anatomy[label_slice == 6] = [0.5, 0.5, 1]  # RV blood - lighter blue
#     anatomy[label_slice == 7] = [1, 0.8, 0.5]  # LA blood - light orange
#     anatomy[label_slice == 8] = [0.5, 0.85, 1] # RA blood - very light blue
    
#     ax.imshow(anatomy)
    
#     # Check circularity of structure in this slice
#     structure_pixels = np.zeros_like(label_slice, dtype=bool)
#     for label in axis_labels:
#         structure_pixels |= (label_slice == label)
    
#     if np.sum(structure_pixels) > 100:  # If enough pixels
#         structure_coords_2d = np.argwhere(structure_pixels)
#         center_2d = np.mean(structure_coords_2d, axis=0)
#         distances = np.sqrt(np.sum((structure_coords_2d - center_2d)**2, axis=1))
#         circularity = np.std(distances) / np.mean(distances)
#         ax.set_title(f'Slice {slice_idx}\nCircularity: {circularity:.3f}')
#     else:
#         ax.set_title(f'Slice {slice_idx}')
    
#     ax.axis('off')

# plt.tight_layout()
# plt.show()

# # Create cine display comparing manual vs automatic
# seq_to_animate = 'T2w_SE'
# fig3, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))
# fig3.suptitle(f'{sequences[seq_to_animate]["description"]} - Cine Comparison', fontsize=16)

# # Get all frames
# axial_frames = [weighted_images[seq_to_animate][i][:, :, z_slice] for i in range(n_frames)]
# manual_frames = [short_axis_volumes[seq_to_animate][i][manual_sa_slice_idx, :, :] for i in range(n_frames)]
# auto_frames = [auto_short_axis_volumes[seq_to_animate][i][auto_sa_slice_idx, :, :] for i in range(n_frames)]

# # Find display ranges
# axial_vmin = min(np.percentile(img, 1) for img in axial_frames)
# axial_vmax = max(np.percentile(img, 99) for img in axial_frames)
# manual_vmin = min(np.percentile(img, 1) for img in manual_frames)
# manual_vmax = max(np.percentile(img, 99) for img in manual_frames)
# auto_vmin = min(np.percentile(img, 1) for img in auto_frames)
# auto_vmax = max(np.percentile(img, 99) for img in auto_frames)

# # Initial images
# im1 = ax1.imshow(axial_frames[0], cmap='gray', vmin=axial_vmin, vmax=axial_vmax)
# im2 = ax2.imshow(manual_frames[0], cmap='gray', vmin=manual_vmin, vmax=manual_vmax)
# im3 = ax3.imshow(auto_frames[0], cmap='gray', vmin=auto_vmin, vmax=auto_vmax)

# ax1.set_title('Axial View')
# ax2.set_title('Manual Short-Axis')
# ax3.set_title('Automatic Short-Axis')
# for ax in [ax1, ax2, ax3]:
#     ax.axis('off')

# frame_text = fig3.text(0.5, 0.02, '', ha='center', fontsize=12)

# def animate_comparison_3way(frame):
#     im1.set_array(axial_frames[frame])
#     im2.set_array(manual_frames[frame])
#     im3.set_array(auto_frames[frame])
#     frame_text.set_text(f'Frame {frame + 1}/{n_frames}')
#     return [im1, im2, im3, frame_text]

# anim_3way = FuncAnimation(fig3, animate_comparison_3way, frames=n_frames, 
#                          interval=200, blit=True, repeat=True)
# plt.show()

# print("\nAutomatic short-axis view finding complete!")
# print("The PCA-based method automatically aligns the cardiac long axis with the Z-axis,")
# print("making short-axis slices perpendicular to the LV long axis without manual parameters.")
# print("\nKey improvements:")
# print("- Now uses both LV wall (label 1) and blood (label 5) for more accurate axis detection")
# print("- Shows the angle difference between manual and automatic orientations")
# print("- Validates the circularity of LV in short-axis slices")

# # Create a comprehensive comparison figure showing both manual and auto results side by side
# fig4 = plt.figure(figsize=(20, 12))
# fig4.suptitle(f'Comprehensive Comparison: Manual vs Automatic Short-Axis ({structure_name})', fontsize=16)

# # Get middle slice indices
# manual_mid = short_axis_volumes[seq_to_show][0].shape[0] // 2
# auto_mid = auto_short_axis_volumes[seq_to_show][0].shape[0] // 2

# # Show 5 slices from each method
# n_comparison_slices = 5
# slice_offsets = [-20, -10, 0, 10, 20]

# for i, offset in enumerate(slice_offsets):
#     # Manual method
#     ax_manual = plt.subplot(3, n_comparison_slices, i + 1)
#     manual_idx = manual_mid + offset
#     if 0 <= manual_idx < short_axis_volumes[seq_to_show][0].shape[0]:
#         manual_slice = short_axis_volumes[seq_to_show][0][manual_idx, :, :]
#         ax_manual.imshow(manual_slice, cmap='gray')
#     ax_manual.set_title(f'Manual\nSlice {manual_idx}')
#     ax_manual.axis('off')
    
#     # Automatic method
#     ax_auto = plt.subplot(3, n_comparison_slices, i + 1 + n_comparison_slices)
#     auto_idx = auto_mid + offset
#     if 0 <= auto_idx < auto_short_axis_volumes[seq_to_show][0].shape[0]:
#         auto_slice = auto_short_axis_volumes[seq_to_show][0][auto_idx, :, :]
#         ax_auto.imshow(auto_slice, cmap='gray')
#     ax_auto.set_title(f'Auto\nSlice {auto_idx}')
#     ax_auto.axis('off')
    
#     # Show labeled anatomy for automatic method
#     ax_labels = plt.subplot(3, n_comparison_slices, i + 1 + 2*n_comparison_slices)
#     if 0 <= auto_idx < reoriented_labels.shape[0]:
#         label_slice = reoriented_labels[auto_idx, :, :]
#         anatomy = np.zeros((*label_slice.shape, 3))
#         anatomy[label_slice == 1] = [1, 0, 0]      # LV wall - red
#         anatomy[label_slice == 2] = [0, 0, 1]      # RV wall - blue
#         anatomy[label_slice == 5] = [1, 0.7, 0.7]  # LV blood - light red
#         anatomy[label_slice == 6] = [0.5, 0.5, 1]  # RV blood - lighter blue
#         ax_labels.imshow(anatomy)
        
#         # Calculate LV metrics
#         lv_pixels = (label_slice == 1) | (label_slice == 5)
#         if np.sum(lv_pixels) > 50:
#             lv_coords_2d = np.argwhere(lv_pixels)
#             center_2d = np.mean(lv_coords_2d, axis=0)
#             distances = np.sqrt(np.sum((lv_coords_2d - center_2d)**2, axis=1))
#             circularity = np.std(distances) / (np.mean(distances) + 1e-6)
#             ax_labels.set_title(f'Labels\nCirc: {circularity:.2f}')
#         else:
#             ax_labels.set_title('Labels')
#     ax_labels.axis('off')

# plt.tight_layout()
# plt.show()

# # Create a dedicated figure for showing the cutting plane clearly
# fig_cutting = plt.figure(figsize=(15, 10))
# fig_cutting.suptitle('Detailed Cutting Plane Visualization', fontsize=16)

# # Get the reference axial slice
# ref_axial_slice = weighted_images[seq_to_show][frame_to_show][:, :, z_slice]
# ref_labels_slice = frame_data[frame_to_show]['Labels'][:, :, z_slice]

# # Note: labels, lv_coords_3d, angle_degrees_global were already calculated above
# long_axis = ref_transform_info['long_axis']
# centroid_3d = ref_transform_info['centroid']

# # Create a larger subplot for better visibility
# ax_main = plt.subplot(2, 2, 1)

# # Show the image with enhanced contrast
# img_display = ref_axial_slice.copy()
# img_display = (img_display - np.percentile(img_display, 5)) / (np.percentile(img_display, 95) - np.percentile(img_display, 5))
# img_display = np.clip(img_display, 0, 1)

# # Create RGB version
# img_rgb = np.stack([img_display, img_display, img_display], axis=-1)

# # Overlay LV mask with transparency
# lv_wall_mask = (ref_labels_slice == 1)
# lv_blood_mask = (ref_labels_slice == 5)

# # Create colored overlay
# overlay = np.zeros_like(img_rgb)
# overlay[lv_wall_mask] = [1, 0, 0]  # Red for wall
# overlay[lv_blood_mask] = [1, 0.5, 0.5]  # Light red for blood

# # Blend with original
# alpha = 0.3
# img_rgb = img_rgb * (1 - alpha) + overlay * alpha

# ax_main.imshow(img_rgb)

# # Mark the 3D centroid projection
# ax_main.plot(centroid_3d[2], centroid_3d[1], 'go', markersize=15, 
#              markeredgecolor='black', markeredgewidth=2, label='3D LV centroid')

# # Calculate 2D LV centroid in this slice
# lv_pixels_2d = np.argwhere(lv_wall_mask | lv_blood_mask)
# if len(lv_pixels_2d) > 0:
#     centroid_2d = np.mean(lv_pixels_2d, axis=0)
#     ax_main.plot(centroid_2d[1], centroid_2d[0], 'yo', markersize=15,
#                  markeredgecolor='black', markeredgewidth=2, label='2D slice centroid')

# # Draw the long axis projection with better visibility
# if abs(long_axis[2]) < 0.99:
#     # Project onto axial plane
#     proj_long = long_axis.copy()
#     proj_long[2] = 0
#     proj_long = proj_long / (np.linalg.norm(proj_long) + 1e-6)
    
#     # Draw thick arrow for long axis
#     arrow_len = 80
#     ax_main.arrow(centroid_3d[2], centroid_3d[1],
#                   proj_long[1] * arrow_len, proj_long[0] * arrow_len,
#                   head_width=12, head_length=10, fc='blue', ec='black',
#                   linewidth=4, label='Long axis projection')
    
#     # Calculate perpendicular (short axis direction)
#     perp = np.array([-proj_long[1], proj_long[0]])
    
#     # Draw the cutting plane as a thick line
#     line_len = 150
#     x_line = [centroid_3d[2] - perp[0]*line_len, centroid_3d[2] + perp[0]*line_len]
#     y_line = [centroid_3d[1] - perp[1]*line_len, centroid_3d[1] + perp[1]*line_len]
#     ax_main.plot(x_line, y_line, 'c-', linewidth=4, label='Auto SA cutting plane')
    
#     # Add perpendicular marks
#     for t in [-0.5, 0, 0.5]:
#         x_mark = centroid_3d[2] + t * perp[0] * line_len
#         y_mark = centroid_3d[1] + t * perp[1] * line_len
#         ax_main.plot(x_mark, y_mark, 'co', markersize=8)

# # Add grid for reference
# ax_main.grid(True, alpha=0.3, linestyle='--')
# ax_main.set_title(f'Axial Slice {z_slice} with Automatic Cutting Plane', fontsize=14)
# ax_main.legend(loc='upper right', fontsize=10)
# ax_main.set_xlabel('X')
# ax_main.set_ylabel('Y')

# # Subplot 2: Show manual cutting angles
# ax_manual = plt.subplot(2, 2, 2)
# ax_manual.text(0.1, 0.9, 'Manual Method Angles:', fontsize=14, weight='bold')
# ax_manual.text(0.1, 0.7, 'Step 1: θ = -45° (VLA)', fontsize=12)
# ax_manual.text(0.1, 0.6, 'Step 2: θ = -65° (HLA)', fontsize=12)
# ax_manual.text(0.1, 0.5, 'Step 3: θ = 0° (SA)', fontsize=12)

# # Show the approximate manual cutting direction
# manual_angle_rad = -45 * np.pi / 180  # First rotation
# manual_dir = np.array([np.cos(manual_angle_rad), np.sin(manual_angle_rad)])
# ax_manual.arrow(0.5, 0.3, manual_dir[0]*0.2, manual_dir[1]*0.2,
#                 head_width=0.05, head_length=0.03, fc='red', ec='red')
# ax_manual.text(0.7, 0.3, 'Initial cut direction', fontsize=10)
# ax_manual.set_xlim(0, 1)
# ax_manual.set_ylim(0, 1)
# ax_manual.axis('off')

# # Subplot 3: Quantitative comparison
# ax_quant = plt.subplot(2, 2, 3)
# ax_quant.text(0.1, 0.9, 'Quantitative Analysis:', fontsize=14, weight='bold')
# ax_quant.text(0.1, 0.7, f'Long axis vector: [{long_axis[0]:.3f}, {long_axis[1]:.3f}, {long_axis[2]:.3f}]', fontsize=11)
# ax_quant.text(0.1, 0.6, f'Tilt from vertical: {np.arccos(abs(long_axis[2])) * 180/np.pi:.1f}°', fontsize=11)
# ax_quant.text(0.1, 0.5, f'Azimuth angle: {np.arctan2(long_axis[1], long_axis[0]) * 180/np.pi:.1f}°', fontsize=11)
# ax_quant.text(0.1, 0.3, f'Manual vs Auto angle: {angle_degrees_global:.1f}°', fontsize=12, color='red')
# ax_quant.text(0.1, 0.1, 'Large angle difference indicates', fontsize=10, style='italic')
# ax_quant.text(0.1, 0.05, 'manual method may not match anatomy', fontsize=10, style='italic')
# ax_quant.axis('off')

# # Subplot 4: Show z-projection to understand 3D orientation
# ax_z = plt.subplot(2, 2, 4)
# # Create a sagittal-like view showing the long axis
# z_indices = np.unique(lv_coords_3d[:, 0])
# y_center = int(centroid_3d[1])

# if y_center >= 0 and y_center < labels.shape[1]:
#     sagittal_slice = labels[:, y_center, :]
#     sagittal_display = np.zeros(sagittal_slice.shape)
#     sagittal_display[sagittal_slice == 1] = 1  # LV wall
#     sagittal_display[sagittal_slice == 5] = 0.5  # LV blood
    
#     ax_z.imshow(sagittal_display.T, cmap='hot', origin='lower', aspect='auto')
    
#     # Show long axis on sagittal view
#     if len(z_indices) > 1:
#         z_range = max(z_indices) - min(z_indices)
#         x_range = 50  # Approximate
        
#         # Scale the long axis vector for display
#         scale = min(z_range, x_range) * 0.4
#         z_end = centroid_3d[0] + long_axis[0] * scale
#         x_end = centroid_3d[2] + long_axis[2] * scale
        
#         ax_z.arrow(centroid_3d[2], centroid_3d[0], 
#                    long_axis[2] * scale, long_axis[0] * scale,
#                    head_width=5, head_length=3, fc='cyan', ec='cyan', linewidth=2)
#         ax_z.plot(centroid_3d[2], centroid_3d[0], 'go', markersize=8)
        
#     ax_z.set_title('Sagittal View (Y={})'.format(y_center))
#     ax_z.set_xlabel('X')
#     ax_z.set_ylabel('Z')
# else:
#     ax_z.text(0.5, 0.5, 'Sagittal view not available', ha='center', va='center')
#     ax_z.axis('off')

# plt.tight_layout()
# plt.show()

# print("\nCutting Plane Debug Information:")
# print(f"- Axial slice shown: {z_slice}")
# print(f"- 3D centroid Z position: {centroid_3d[0]:.1f}")
# print(f"- Distance from slice to centroid: {abs(z_slice - centroid_3d[0]):.1f} slices")
# print(f"- Long axis is tilted {np.arccos(abs(long_axis[2])) * 180/np.pi:.1f}° from vertical")
# print(f"- In-plane rotation: {np.arctan2(long_axis[1], long_axis[0]) * 180/np.pi:.1f}°")

# print(f"\nOrientation difference between manual and automatic methods: {angle_degrees_global:.1f}°")
# print("Lower circularity values indicate better short-axis alignment (perfect circle = 0)")

# print("\nAutomatic short-axis processing complete!")
# print("\nTo crop the automatic short-axis volumes, you can use:")
# print("cropped_auto_short_axis_volumes = crop_short_axis_volumes(")
# print("    short_axis_volumes=auto_short_axis_volumes,")
# print("    crop_size=90")
# print(")")
# print("\nKey debugging insights:")
# print(f"- Structure is tilted {np.arccos(abs(ref_transform_info['long_axis'][2])) * 180/np.pi:.1f}° from vertical")
# print(f"- Manual method assumes fixed angles, auto method adapts to actual anatomy")
# print("\nWhat to look for in the visualizations:")
# print("1. The cyan line on the axial view shows where the automatic short-axis plane cuts")
# print("2. The blue arrow shows the structure's long axis projected onto the axial plane")
# print("3. Compare the cutting plane angle with the anatomy (colored overlay)")
# print("4. The sagittal view shows the true 3D orientation")
# print("5. If the angle difference is large (>20°), the methods are finding different orientations")
# print("\n" + "="*70)
# print("IMPORTANT: If you change axis_labels, restart the kernel or clear variables")
# print("to ensure the transformation is recalculated with the new labels!")
# print("="*70)

def crop_short_axis_volumes(short_axis_volumes, crop_size=90):
    """
    Crop 3D volumes across all modalities and time frames to remove padding.
    
    Parameters:
    -----------
    short_axis_volumes : dict
        Dictionary where keys are sequence names and values are lists of 3D volumes for each time frame
    crop_size : int
        Size of the cube to crop (will be crop_size x crop_size x crop_size)
    
    Returns:
    --------
    cropped_volumes : dict
        Dictionary with same structure but containing cropped volumes
    """
    import numpy as np
    
    # Initialize structure to hold cropped volumes
    cropped_volumes = {}
    
    # First pass: find the center of all non-zero regions across all volumes
    print("Finding centers of all volumes...")
    all_z_indices = []
    all_y_indices = []
    all_x_indices = []
    
    # Loop through all sequences and frames to find global center
    for seq_name, frame_volumes in short_axis_volumes.items():
        for frame_idx, volume in enumerate(frame_volumes):
            # Find non-zero voxels
            z_indices, y_indices, x_indices = np.where(volume > 0)
            if len(z_indices) > 0:  # If non-zero voxels exist
                all_z_indices.extend(z_indices)
                all_y_indices.extend(y_indices)
                all_x_indices.extend(x_indices)
    
    # Calculate center of all non-zero voxels
    center_z = int(np.mean(all_z_indices))
    center_y = int(np.mean(all_y_indices))
    center_x = int(np.mean(all_x_indices))
    
    print(f"Global center of interest: z={center_z}, y={center_y}, x={center_x}")
    
    # Second pass: crop all volumes around this center
    half_size = crop_size // 2
    
    for seq_name, frame_volumes in short_axis_volumes.items():
        cropped_volumes[seq_name] = []
        
        for frame_idx, volume in enumerate(frame_volumes):
            depth, height, width = volume.shape
            
            # Calculate crop boundaries
            z_min = max(0, center_z - half_size)
            z_max = min(depth, center_z + half_size)
            y_min = max(0, center_y - half_size)
            y_max = min(height, center_y + half_size)
            x_min = max(0, center_x - half_size)
            x_max = min(width, center_x + half_size)
            
            # Adjust if crop region is smaller than desired size
            if z_max - z_min < crop_size:
                if z_min == 0:
                    z_max = min(depth, crop_size)
                elif z_max == depth:
                    z_min = max(0, depth - crop_size)
            
            if y_max - y_min < crop_size:
                if y_min == 0:
                    y_max = min(height, crop_size)
                elif y_max == height:
                    y_min = max(0, height - crop_size)
            
            if x_max - x_min < crop_size:
                if x_min == 0:
                    x_max = min(width, crop_size)
                elif x_max == width:
                    x_min = max(0, width - crop_size)
            
            # Crop the volume
            cropped_volume = volume[z_min:z_max, y_min:y_max, x_min:x_max]
            
            # Check if cropped volume is smaller than desired size (due to volume boundaries)
            # If so, pad with zeros to reach desired size
            current_depth, current_height, current_width = cropped_volume.shape
            
            if (current_depth < crop_size or 
                current_height < crop_size or 
                current_width < crop_size):
                
                # Create zero-filled volume of desired size
                padded_volume = np.zeros((crop_size, crop_size, crop_size))
                
                # Calculate padding amounts for each dimension
                pad_depth = max(0, crop_size - current_depth) // 2
                pad_height = max(0, crop_size - current_height) // 2
                pad_width = max(0, crop_size - current_width) // 2
                
                # Insert cropped volume into center of padded volume
                padded_volume[
                    pad_depth:pad_depth+current_depth,
                    pad_height:pad_height+current_height,
                    pad_width:pad_width+current_width
                ] = cropped_volume
                
                cropped_volume = padded_volume
            
            cropped_volumes[seq_name].append(cropped_volume)
            print(f"Cropped {seq_name} frame {frame_idx+1}: shape {cropped_volume.shape}")
    
    return cropped_volumes

# Crop all volumes
crop_size=90
cropped_short_axis_volumes = crop_short_axis_volumes(
    short_axis_volumes=short_axis_volumes,
    crop_size=90  # Adjust crop size as needed
)

print("\nCropping complete!")


# Animated cine display of cropped short-axis volumes
from matplotlib.animation import FuncAnimation
from IPython.display import HTML
import numpy as np
import matplotlib.pyplot as plt

# Select sequence and slice for animation
seq_to_animate = list(cropped_short_axis_volumes.keys())[1]  # Get third sequence (can be changed)
frame_idx = 0  # First frame for slice calculation
middle_slice_idx = cropped_short_axis_volumes[seq_to_animate][frame_idx].shape[0] // 2 - 10

print(f"Creating animation for: {sequences[seq_to_animate]['description']}")
print(f"Using slice index: {middle_slice_idx}")
print(f"Volume shape: {cropped_short_axis_volumes[seq_to_animate][frame_idx].shape}")
print(f"Number of frames: {len(cropped_short_axis_volumes[seq_to_animate])}")

# Create figure and axis
fig, ax = plt.subplots(figsize=(10, 8))
fig.suptitle(f'{sequences[seq_to_animate]["description"]} Cine - Cropped Volume (Slice {middle_slice_idx})', 
             fontsize=16)

# Find consistent display range for animation across all frames
all_images = [vol[middle_slice_idx, :, :] for vol in cropped_short_axis_volumes[seq_to_animate]]
vmin = min(np.percentile(img, 1) for img in all_images)
vmax = max(np.percentile(img, 99) for img in all_images)

print(f"Display range: {vmin:.2f} to {vmax:.2f}")

# Initial image setup
initial_image = cropped_short_axis_volumes[seq_to_animate][0][middle_slice_idx, :, :]
im = ax.imshow(initial_image, cmap='gray', vmin=vmin, vmax=vmax)
cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
ax.axis('off')

# Add frame counter text
frame_text = ax.text(0.02, 0.98, '', transform=ax.transAxes, 
                     color='white', fontsize=14, va='top', weight='bold',
                     bbox=dict(boxstyle='round', facecolor='black', alpha=0.7))

# Add cardiac phase information
phase_text = ax.text(0.02, 0.02, '', transform=ax.transAxes, 
                     color='white', fontsize=12, va='bottom',
                     bbox=dict(boxstyle='round', facecolor='blue', alpha=0.7))

def animate(frame):
    """Animation function for each frame"""
    # Update image data
    current_image = cropped_short_axis_volumes[seq_to_animate][frame][middle_slice_idx, :, :]
    im.set_array(current_image)
    
    # Update frame counter
    frame_text.set_text(f'Frame {frame + 1}/{len(cropped_short_axis_volumes[seq_to_animate])}')
    
    # Add cardiac phase information (assuming frames represent cardiac cycle)
    total_frames = len(cropped_short_axis_volumes[seq_to_animate])
    cardiac_phase = (frame / (total_frames - 1)) * 100  # Percentage through cardiac cycle
    
    if cardiac_phase < 40:
        phase_name = "Diastole"
    elif cardiac_phase < 60:
        phase_name = "Systole"
    else:
        phase_name = "Diastole"
    
    phase_text.set_text(f'{phase_name}\n{cardiac_phase:.1f}% cycle')
    
    return [im, frame_text, phase_text]

# Create animation
n_frames = len(cropped_short_axis_volumes[seq_to_animate])
print(f"Creating animation with {n_frames} frames...")

anim = FuncAnimation(fig, animate, frames=n_frames, interval=300, blit=True, repeat=True)

# Display the animation
try:
    # For Jupyter notebook
    anim_html = HTML(anim.to_html5_video())
    display(anim_html)
except:
    # Fallback: just show the figure
    plt.show()

# Also create a static comparison showing multiple frames
print("\nCreating static frame comparison...")

# Show multiple frames in a grid
n_display_frames = min(6, n_frames)
frame_indices = np.linspace(0, n_frames-1, n_display_frames, dtype=int)

fig2, axes = plt.subplots(2, 3, figsize=(15, 10))
fig2.suptitle(f'{sequences[seq_to_animate]["description"]} - Key Cardiac Phases (Slice {middle_slice_idx})', 
              fontsize=16)

axes = axes.flatten()
for i, frame_idx in enumerate(frame_indices):
    if i < len(axes):
        ax = axes[i]
        image = cropped_short_axis_volumes[seq_to_animate][frame_idx][middle_slice_idx, :, :]
        
        im = ax.imshow(image, cmap='gray', vmin=vmin, vmax=vmax)
        
        # Calculate cardiac phase
        cardiac_phase = (frame_idx / (n_frames - 1)) * 100
        if cardiac_phase < 40:
            phase_name = "Diastole"
        elif cardiac_phase < 60:
            phase_name = "Systole"
        else:
            phase_name = "Diastole"
            
        ax.set_title(f'Frame {frame_idx + 1}\n{phase_name} ({cardiac_phase:.1f}%)')
        ax.axis('off')
        
        # Add colorbar to each subplot
        plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)

# Hide any unused subplots
for i in range(n_display_frames, len(axes)):
    axes[i].axis('off')

plt.tight_layout()
plt.show()

# Create a comparison across all available sequences
print(f"\nCreating comparison across all sequences...")

fig3, axes = plt.subplots(len(sequences), n_frames, figsize=(3*n_frames, 3*len(sequences)))
if len(sequences) == 1:
    axes = axes.reshape(1, -1)
fig3.suptitle(f'All Sequences - Cardiac Cycle Comparison (Slice {middle_slice_idx})', fontsize=16)

for seq_idx, (seq_name, seq_info) in enumerate(sequences.items()):
    for frame_idx in range(n_frames):
        if len(sequences) > 1:
            ax = axes[seq_idx, frame_idx]
        else:
            ax = axes[frame_idx]
            
        # Get image data
        image = cropped_short_axis_volumes[seq_name][frame_idx][middle_slice_idx, :, :]
        
        # Find display range for this sequence
        seq_images = [vol[middle_slice_idx, :, :] for vol in cropped_short_axis_volumes[seq_name]]
        seq_vmin = np.percentile(np.concatenate([img.flatten() for img in seq_images]), 1)
        seq_vmax = np.percentile(np.concatenate([img.flatten() for img in seq_images]), 99)
        
        im = ax.imshow(image, cmap='gray', vmin=seq_vmin, vmax=seq_vmax)
        
        # Add titles
        if frame_idx == 0:
            ax.set_ylabel(f'{seq_info["description"]}\n{seq_name}', fontsize=10)
        if seq_idx == 0:
            cardiac_phase = (frame_idx / (n_frames - 1)) * 100
            ax.set_title(f'Frame {frame_idx + 1}\n({cardiac_phase:.0f}%)', fontsize=10)
            
        ax.axis('off')

plt.tight_layout()
plt.show()

print(f"\nAnimation complete!")
print(f"Sequence: {sequences[seq_to_animate]['description']}")
print(f"Slice: {middle_slice_idx} (middle slice)")
print(f"Frames: {n_frames}")
print(f"Image size: {cropped_short_axis_volumes[seq_to_animate][0].shape}")

import numpy as np
import matplotlib.pyplot as plt

def qdf(a, b, c):
    """
    Solves quadratic equation ax^2 + bx + c = 0
    
    Returns:
        Roots of the quadratic equation
    """
    d = b**2 - 4*a*c
    if d < 0:
        # Handle negative discriminant case
        d = complex(d, 0)
    root1 = (-b + np.sqrt(d)) / (2*a)
    root2 = (-b - np.sqrt(d)) / (2*a)
    return np.array([root1, root2])

def findq2r2(smax, gmax, r, r1, T, Ts, N, Fcoeff, rmax, z=0):
    """
    Calculates the second derivatives of r and theta (q) that satisfy hardware and FOV constraints
    
    Parameters:
        smax: Maximum slew rate in G/cm/s
        gmax: Maximum gradient amplitude in G/cm
        r: Current value of the k-space radius
        r1: Current derivative of r
        T: Gradient sample period
        Ts: Data sampling period
        N: Number of spiral interleaves
        Fcoeff: FOV coefficients for variable density
        rmax: Maximum k-space radius
        z: R/L for gradient coil (voltage model parameter)
    
    Returns:
        q2: Second derivative of angle theta
        r2: Second derivative of radius r
    """
    gamma = 4258  # Hz/G

    smax = smax + z*gmax

    # # Calculate FOV and its derivative for current r
    F = 0
    dFdr = 0
    for rind in range(len(Fcoeff)):
        F += Fcoeff[rind] * (r/rmax)**(rind)
        if rind > 0:
            dFdr += rind * Fcoeff[rind] * (r/rmax)**(rind-1) / rmax

    # FOV limit on gradient
    GmaxFOV = N/gamma / F / Ts
    if not hasattr(findq2r2, "printed"):
        print(f'Required GmaxFOV: {GmaxFOV}')
        findq2r2.printed = True
    Gmax = min(GmaxFOV, gmax)
    # Gmax = gmax

    # Maximum allowed r1 based on gradient amplitude limit
    maxr1 = np.sqrt((gamma*Gmax)**2 / (1 + (2*np.pi*F*r/N)**2))

    if r1 > maxr1:
        # Gradient amplitude limited case
        r2 = (maxr1 - r1) / T
    else:
        # Slew rate limited case
        twopiFoN = 2*np.pi*F/N
        twopiFoN2 = twopiFoN**2

        # Coefficients for the quadratic equation in r2
        A = 1 + twopiFoN2*r*r
        B = 2*twopiFoN2*r*r1*r1 + 2*twopiFoN2/F*dFdr*r*r*r1*r1 + 2*z*r1 + 2*twopiFoN2*r1*r
        C1 = twopiFoN2**2*r*r*r1**4 + 4*twopiFoN2*r1**4 + (2*np.pi/N*dFdr)**2*r*r*r1**4 + 4*twopiFoN2/F*dFdr*r*r1**4 - (gamma)**2*smax**2
        C2 = z*(z*r1**2 + z*twopiFoN2*r1**2 + 2*twopiFoN2*r1**3*r + 2*twopiFoN2/F*dFdr*r1**3*r)
        C = C1 + C2

        # Solve quadratic equation
        rts = qdf(A, B, C)
        r2 = np.real(rts[0])  # Use first root

        # Calculate and check resulting slew rate
        slew = 1/gamma * (r2 - twopiFoN2*r*r1**2 + 1j*twopiFoN*(2*r1**2 + r*r2 + dFdr/F*r*r1**2))
        sr = np.abs(slew)/smax

        if sr > 1.01:
            print(f"Slew violation, slew = {round(np.abs(slew))}, smax = {round(smax)}, sr={sr:.3f}, r={r:.3f}, r1={r1:.3f}")

    # Calculate q2 from other parameters
    q2 = 2*np.pi/N*dFdr*r1**2 + 2*np.pi*F/N*r2
    
    return q2, r2

def vds(smax, gmax, T, N, Fcoeff, rmax, z=0):
    """
    Variable Density Spiral trajectory generation
    
    Parameters:
        smax: Maximum slew rate G/cm/s
        gmax: Maximum gradient G/cm
        T: Sampling period (s)
        N: Number of interleaves
        Fcoeff: FOV coefficients - FOV(r) = Sum_k Fcoeff[k]*(r/rmax)^k
        rmax: Maximum k-space radius (cm^-1)
        z: R/L for gradient coil model
        
    Returns:
        k: k-space trajectory (kx+iky) in cm^-1
        g: gradient waveform (Gx+iGy) in G/cm
        s: derivative of g (Sx+iSy) in G/cm/s
        time: time points corresponding to trajectory (s)
        r: k-space radius vs time
        theta: angle vs time
    """
    print('Variable Density Spiral Generation')
    
    gamma = 4258  # Hz/G

    # Oversampling for trajectory calculation
    oversamp = 8  # Keep this even
    To = T / oversamp  # Oversampled period

    # Initialize variables
    q0 = 0
    q1 = 0
    r0 = 0
    r1 = 0
    t = 0
    count = 0

    # Pre-allocate arrays (can extend later if needed)
    max_points = 10000000
    theta = np.zeros(max_points)
    r = np.zeros(max_points)
    time = np.zeros(max_points)

    # Main loop to generate trajectory
    while r0 < rmax:
        # Get the next point on the trajectory
        q2, r2 = findq2r2(smax, gmax, r0, r1, To, T, N, Fcoeff, rmax, z)

        # Integrate for θ, θ', r, and r'
        q1 = q1 + q2 * To
        q0 = q0 + q1 * To
        t = t + To

        r1 = r1 + r2 * To
        r0 = r0 + r1 * To

        # Store values
        count += 1
        theta[count] = q0
        r[count] = r0
        time[count] = t

        if count % 10000 == 0:
            print(f'{count} points, |k|={r0:.6f}')

        # Break if we've reached array limit
        if count >= max_points - 1:
            print("Warning: reached maximum array size")
            break

    # Trim arrays to used size
    theta = theta[:count+1]
    r = r[:count+1]
    time = time[:count+1]

    # Downsample to original sampling rate
    theta_ds = theta[oversamp//2::oversamp]
    r_ds = r[oversamp//2::oversamp]
    time_ds = time[oversamp//2::oversamp]

    # Keep the length a multiple of 4 (to match original code)
    length = 4 * (len(theta_ds) // 4)
    theta_ds = theta_ds[:length]
    r_ds = r_ds[:length]
    time_ds = time_ds[:length]

    # Calculate k-space trajectory, gradients, and slew rates
    k = r_ds * np.exp(1j * theta_ds)
    
    # Calculate gradients
    g = np.zeros_like(k, dtype=complex)
    g[:-1] = (k[1:] - k[:-1]) / T / gamma
    g[-1] = g[-2]  # Extrapolate last point
    
    # Calculate slew rates
    s = np.zeros_like(g, dtype=complex)
    s[:-1] = (g[1:] - g[:-1]) / T
    s[-1] = s[-2]  # Extrapolate last point

    # Plot results
    plot_vds_results(time_ds, k, g, s)

    return k, g, s, time_ds, r_ds, theta_ds

def plot_vds_results(time, k, g, s):
    """
    Plot the results of the VDS trajectory generation
    """
    # Undersample for plotting
    tp = time[::10]
    kp = k[::10]
    gp = g[::10]
    sp = s[::10]
    
    plt.figure(figsize=(12, 10))
    
    # Plot 1: k-space trajectory
    plt.subplot(2, 2, 1)
    plt.plot(np.real(kp), np.imag(kp))
    plt.title('ky vs kx')
    plt.xlabel('kx (cm$^{-1}$)')
    plt.ylabel('ky (cm$^{-1}$)')
    plt.axis('square')
    plt.grid(True)
    
    # Plot 2: k-space vs time
    plt.subplot(2, 2, 2)
    plt.plot(tp, np.real(kp), 'c-', label='kx')
    plt.plot(tp, np.imag(kp), 'g-', label='ky')
    plt.title('k-space vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('k (cm$^{-1}$)')
    plt.legend()
    plt.grid(True)
    
    # Plot 3: Gradient vs time
    plt.subplot(2, 2, 3)
    plt.plot(tp, np.real(gp), 'c-', label='Gx')
    plt.plot(tp, np.imag(gp), 'g-', label='Gy')
    plt.title('Gradient vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('g (G/cm)')
    plt.legend()
    plt.grid(True)
    
    # Plot 4: Slew rate vs time
    plt.subplot(2, 2, 4)
    plt.plot(tp, np.real(sp), 'c-', label='Sx')
    plt.plot(tp, np.imag(sp), 'g-', label='Sy')
    plt.plot(tp, np.abs(sp), 'k-', label='|S|')
    plt.title('Slew-Rate vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('s (G/cm/s)')
    plt.legend()
    plt.grid(True)
    
    plt.figure(figsize=(12, 10))
    
    # Plot 5: Gradient Mag vs time
    plt.subplot(2, 1, 1)
    plt.plot(tp, np.abs(gp), 'c-', label='G')
    plt.title('Gradient Magnitude vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('g (G/cm)')
    plt.legend()
    plt.grid(True)
    
    # Plot 4: Slew rate vs time
    plt.subplot(2, 1, 2)
    plt.plot(tp, np.abs(sp), 'k-', label='|S|')
    plt.title('Slew-Rate vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('s (G/cm/s)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()

# Example parameters
smax = 20000       # Maximum slew rate (G/cm/s) 200T/m/s
gmax = 20           # Maximum gradient amplitude (G/cm) 40 mt/m
T = 2e-6           # Sampling period (s)
N = 1    # Number of interleaves
# Fcoeff = [192, -168]   # FOV = 18 cm (constant)或者等效地 [19.2, -16.8] (cm单位)
Fcoeff = [18, 0]
sf = 1
rmax = sf * 1/(2*0.2)   # Maximum k-space radius for 2mm resolution

# Example with variable density
# FOV = 24 - 4*(r/rmax) cm (decreases from 24cm to 20cm)
# Fcoeff = [24, -4]


# Generate the spiral trajectory
k, g, s, time, r, theta = vds(smax, gmax, T, N, Fcoeff, rmax)

print(f'matrix size of kspace: {k.shape}')
print(f'maximum radial distance from kspace center: {r.max()}')
print(f'gradient magnitude maximum value: {abs(g.max())}')
print(f'slew rate maximum value: {abs(s.max())}')
print(f'time shape: {time.shape}')
print(f'minimum read out time: {time[-1]*1000} ms')

# Extract kx and ky components from complex k-space trajectory
kx = np.real(k)  # Real part of k (x-component)
ky = np.imag(k)  # Imaginary part of k (y-component)
delta_kx = kx[1] - kx[0]
print(f'delta kx: {delta_kx}')
delta_r = r[1] - r[0]
print(f'delta r: {delta_r}')
# Get maximum k-space radius
k_max = r.max() /sf # Using the r variable already calculated in vds function

print(f"kx min: {np.min(kx)}, kx max: {np.max(kx)}")
print(f"ky min: {np.min(ky)}, ky max: {np.max(ky)}")
print(f"max r: {np.max(np.sqrt(kx**2 + ky**2))}")

# import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata, NearestNDInterpolator
import cv2
from scipy.ndimage import gaussian_filter
# import sigpy as sp
# import sigpy.mri as spmri
from scipy import special
import scipy.sparse as spr
from scipy.sparse.linalg import lsqr

kx_spiral = kx
ky_spiral = ky
image_size = crop_size
# --- MODIFICATION START ---
# Calculate FLOATING POINT pixel coordinates first
kx_pixel_float = ((kx_spiral / k_max) * (image_size/2) + image_size/2)
ky_pixel_float = (ky_spiral / k_max) * (image_size/2) + image_size/2

# The range of ky_pixel_float
ky_pixel_float_range = (np.min(ky_pixel_float), np.max(ky_pixel_float))
print(f'inital range of ky_pixel float: {ky_pixel_float_range}')
kx_pixel_float_range = (np.min(kx_pixel_float), np.max(kx_pixel_float))
print(f'inital range of kx_pixel float: {kx_pixel_float_range}')


# Convert to INTEGER coordinates for visualization and sparse grid indexing later
kx_pixel_int = ((kx_spiral / k_max) * (image_size/2) + image_size/2).astype(int)
ky_pixel_int = ((ky_spiral / k_max) * (image_size/2) + image_size/2).astype(int)
# --- MODIFICATION END ---

# Visualize the spiral trajectory properly (using integer coordinates for plot clarity)
plt.figure(figsize=(15, 5))
plt.subplot(1, 3, 1)
plt.plot(kx_spiral, ky_spiral, 'b.', markersize=1)
plt.title("k-space Spiral Trajectory (cycles/m)")
plt.axis('equal')
plt.grid(True)
theta_vis = np.linspace(0, 2*np.pi, 100) # Define theta if not already defined
plt.plot(k_max*np.cos(theta_vis), k_max*np.sin(theta_vis), 'r--')

plt.subplot(1, 3, 2)
# Use float coordinates for pixel plot
plt.plot(kx_pixel_float-image_size/2, ky_pixel_float-image_size/2, 'b.', markersize=1)
plt.title("Spiral Trajectory (pixel coordinates)")
plt.axis('equal')
plt.grid(True)
plt.plot((image_size/2)*np.cos(theta_vis), (image_size/2)*np.sin(theta_vis), 'r--')

plt.subplot(1, 3, 3)
# Use integer coordinates for pixel plot
plt.plot(kx_pixel_int-image_size/2, ky_pixel_int-image_size/2, 'b.', markersize=1)
plt.title("Spiral Trajectory (pixel coordinates)")
plt.axis('equal')
plt.grid(True)
plt.plot((image_size/2)*np.cos(theta_vis), (image_size/2)*np.sin(theta_vis), 'r--')

plt.tight_layout()
plt.show()

plt.figure(figsize=(7, 7))
plt.plot(kx_pixel_float - image_size/2, ky_pixel_float - image_size/2, 'b.', markersize=2, label='Float coords')
plt.plot(kx_pixel_int - image_size/2, ky_pixel_int - image_size/2, 'ro', markersize=2, label='Int coords', alpha=0.5)
plt.title("Spiral Trajectory: Float vs Int Pixel Coordinates")
plt.axis('equal')
plt.grid(True)
plt.plot((image_size/2)*np.cos(theta_vis), (image_size/2)*np.sin(theta_vis), 'k--', alpha=0.3)
plt.legend()
plt.tight_layout()
plt.show()

# Clip coordinates to valid image coordinates
kx_pixel_float = np.clip(kx_pixel_float, 0, image_size - 1)
ky_pixel_float = np.clip(ky_pixel_float, 0, image_size - 1)

# The range of ky_pixel_float
ky_pixel_float_range = (np.min(ky_pixel_float), np.max(ky_pixel_float))
print(f'clip range of ky_pixel float: {ky_pixel_float_range}')
kx_pixel_float_range = (np.min(kx_pixel_float), np.max(kx_pixel_float))
print(f'clip range of kx_pixel float: {kx_pixel_float_range}')


kx_pixel_int = np.clip(kx_pixel_int, 0, image_size - 1)
ky_pixel_int = np.clip(ky_pixel_int, 0, image_size - 1)

# Prepare grid points for interpolation
kx_cart, ky_cart = np.meshgrid(np.arange(image_size), np.arange(image_size))
kx_cart_flat = kx_cart.flatten()
ky_cart_flat = ky_cart.flatten()
print('kx_cart_flat size ', kx_cart_flat.size)


# Example one slice for one modality
example_slice = cropped_short_axis_volumes[seq_name][frame_idx][middle_slice_idx]
kspace = np.fft.fftshift(np.fft.fft2(example_slice))
image_data = np.abs(example_slice)


kspace_flat = kspace.flatten()
print(f"kspace shape: {kspace_flat.shape}")

# --- MODIFIED INTERPOLATION: KERNEL-BASED APPROACH ---
# Define a Kaiser-Bessel kernel function for interpolation
def kaiser_bessel_kernel(distance, width=2.0, beta=4.0):
    """
    Kaiser-Bessel kernel function for gridding/interpolation
    
    Parameters:
    -----------
    distance : float
        Normalized distance from the sample point (0 to width)
    width : float
        Width of the kernel (typically 2.0-3.0)
    beta : float
        Shape parameter (typically 4.0-13.0)
    
    Returns:
    --------
    kernel_value : float
        Kernel weight
    """
    if distance >= width:
        return 0.0
    
    x = (2.0 * distance / width)
    if x > 1.0:
        return 0.0
    
    # Note: Need to handle potential numerical issues
    if x < 1e-10:
        return 1.0
    
    y = beta * np.sqrt(1.0 - x*x)
    return np.i0(y) / np.i0(beta)  # i0 is modified Bessel function of first kind

# Apply kernel-based interpolation to get data at spiral points
spiral_kspace = np.zeros(len(kx_pixel_float), dtype=complex)
kernel_width_pixels = 1.0  # Kernel width in pixels
kernel_beta = 13.0  # Kernel shape parameter
weight_map = []  # 每个spiral点的权重分布
print("Performing kernel-based interpolation from Cartesian to spiral points...")
for i in range(len(kx_pixel_float)):
    x_center = kx_pixel_float[i]
    y_center = ky_pixel_float[i]
    local_weights = []
    x_min = max(0, int(x_center - kernel_width_pixels))
    x_max = min(image_size-1, int(x_center + kernel_width_pixels))
    y_min = max(0, int(y_center - kernel_width_pixels))
    y_max = min(image_size-1, int(y_center + kernel_width_pixels))
    weighted_sum = 0.0j
    total_weight = 0.0
    for y in range(y_min, y_max+1):
        for x in range(x_min, x_max+1):
            distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
            weight = kaiser_bessel_kernel(distance / kernel_width_pixels, width=1.1, beta=kernel_beta)
            if weight > 0 and kspace[y, x] != 0:
                weighted_sum += weight * kspace[y, x]
                total_weight += weight
                local_weights.append((y, x, weight))
    weight_map.append(local_weights)
    # spiral_kspace[i] = weighted_sum  # 不归一化
    if total_weight > 0:
        spiral_kspace[i] = weighted_sum / total_weight
    else:
        spiral_kspace[i] = 0.0
    

print(f'length of spiral: {spiral_kspace.size}')

# Use a simple radial density compensation
# Use FLOATING POINT coordinates for more accurate radius calculation
kx_rel = kx_pixel_float - image_size/2
ky_rel = ky_pixel_float - image_size/2
radius = np.sqrt(kx_rel**2 + ky_rel**2)
max_radius = image_size/2


# # Apply kernel-based gridding from spiral to Cartesian grid
print("Performing kernel-based gridding from spiral to Cartesian grid...")
# Track contribution weights to normalize properly
weight_grid = np.zeros((image_size, image_size), dtype=float)

# Normalize by sum of weights to avoid density bias
mask = weight_grid > 0
# sparse_kspace[mask] /= weight_grid[mask]

# 假设 weight_map, spiral_kspace, image_size 已经计算好
n_samples = len(spiral_kspace)
n_pixels  = image_size * image_size

rows, cols, vals = [], [], []
b = np.zeros(n_samples, dtype=complex)

for i, local in enumerate(weight_map):
    W = sum(w for (_,_,w) in local)
    b[i] = spiral_kspace[i] * W
    for y, x, w in local:
        idx = y * image_size + x
        rows.append(i); cols.append(idx); vals.append(w)

A = spr.csr_matrix((vals, (rows, cols)), shape=(n_samples, n_pixels))

# 正则化参数 λ 防止病态，可调
λ = 1e-6
# lsqr 支持 damp 参数即 λ
sol = lsqr(A, b, damp=λ, iter_lim=10000)[0]

sparse_kspace = sol.reshape(image_size, image_size)
# sparse_kspace[mask] /= weight_grid[mask]

# Apply a small blur to make the spiral pattern more visible in the visualization
blurred_viz = gaussian_filter(np.abs(sparse_kspace), sigma=1.5)

# Generate coverage mask
coverage_mask = (sparse_kspace != 0).astype(float)

# Reconstruct the image using FFT
reconstructed_image = np.fft.ifft2(np.fft.ifftshift(sparse_kspace))

# de-apod
y, x = np.indices((image_size, image_size))
c = (image_size-1)/2
r = np.sqrt((y-c)**2 + (x-c)**2) / kernel_width_pixels
# KB‐kernel approximation in image domain
apod = np.i0(kernel_beta * np.sqrt(1 - np.minimum(r,1)**2)) / np.i0(kernel_beta)
apod[r>=1] = 0
apod /= apod.max()
# Avoid division by zero
apod += 1e-6

reconstructed_magnitude = np.abs(reconstructed_image) / apod

# Display original image vs reconstruction
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.imshow(image_data, cmap='gray')
plt.title("Original Image")
plt.colorbar()

plt.subplot(1, 2, 2)
plt.imshow(reconstructed_magnitude, cmap='gray')
plt.title("Kernel-based Reconstruction")
plt.colorbar()
plt.tight_layout()
plt.show()

# Calculate k-space coverage
sampled_pixels = np.count_nonzero(sparse_kspace)
total_pixels = image_size * image_size
coverage_percentage = (sampled_pixels / total_pixels) * 100

# Print coverage metric
print(f"K-space Coverage Analysis:")
print(f"  Total k-space pixels: {total_pixels}")
print(f"  Sampled pixels: {sampled_pixels}")
print(f"  Coverage percentage: {coverage_percentage:.2f}%")

# Add coverage visualization to the figure
plt.figure(figsize=(15, 5))
plt.subplot(1, 3, 1)
plt.imshow(np.log(np.abs(kspace) + 1e-10), cmap='gray')
plt.title("Original k-space (Cartesian)")
plt.colorbar()

plt.subplot(1, 3, 2)
plt.imshow(np.log(np.abs(sparse_kspace) + 1e-10), cmap='gray')
plt.title(f"Spiral Sampled k-space\nCoverage: {coverage_percentage:.2f}%")
plt.colorbar()

plt.subplot(1, 3, 3)
plt.imshow(coverage_mask, cmap='binary', vmin=0, vmax=1)
plt.title("K-space Coverage Map")
plt.colorbar(label='Sampled')
plt.tight_layout()
plt.show()

mask = (sparse_kspace != 0)
diff = np.abs(kspace[mask] - sparse_kspace[mask])
print(f"Mean abs error at sampled points: {np.mean(diff):.4e}")
print(f"Max abs error at sampled points: {np.max(diff):.4e}")


# Calculate error metrics
def mse_psnr(ref, cmp):
    mse = np.mean((ref - cmp)**2)
    psnr = 20*np.log10(np.max(ref)/np.sqrt(mse+1e-12))
    return mse, psnr

m1, p1 = mse_psnr(image_data, reconstructed_magnitude)
print(f"No DCF     → MSE={m1:.4e}, PSNR={p1:.2f}dB")

import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata, NearestNDInterpolator
from scipy.ndimage import gaussian_filter
from scipy import special
import scipy.sparse as spr
from scipy.sparse.linalg import lsqr

# First, run the VDS trajectory generation with your parameters
print("Generating spiral trajectory...")
print("=" * 70)
smax = 20000       # Maximum slew rate (G/cm/s) 200T/m/s
gmax = 20          # Maximum gradient amplitude (G/cm) 40 mt/m
T = 2e-6          # Sampling period (s)
N = 1             # Number of interleaves
Fcoeff = [18, 0]  # FOV coefficients
sf = 1
rmax = sf * 1/(2*0.2)   # Maximum k-space radius for 2mm resolution

# Generate the spiral trajectory
k, g, s, time, r, theta = vds(smax, gmax, T, N, Fcoeff, rmax)

print(f'Matrix size of kspace: {k.shape}')
print(f'Maximum radial distance from kspace center: {r.max()}')
print(f'Gradient magnitude maximum value: {abs(g.max())}')
print(f'Slew rate maximum value: {abs(s.max())}')
print(f'Total readout time: {time[-1]*1000:.2f} ms')
print(f'Number of k-space points: {len(k)}')

# Timing clarification:
print("\nTiming Analysis:")
print(f"- Spiral readout duration: {time[-1]*1000:.2f} ms")
print(f"- Number of cardiac frames available: {n_frames}")
print(f"- If each cardiac frame represents ~10ms: {n_frames * 10} ms total")
print("- Motion simulation: Different k-space points will be sampled from different cardiac frames")
print("  based on when they are acquired during the readout")
print("=" * 70)

# Extract kx and ky components
kx = np.real(k)
ky = np.imag(k)
k_max = r.max() / sf

# Kaiser-Bessel kernel function for interpolation
def kaiser_bessel_kernel(distance, width=2.0, beta=4.0):
    """Kaiser-Bessel kernel function for gridding/interpolation"""
    if distance >= width:
        return 0.0
    
    x = (2.0 * distance / width)
    if x > 1.0:
        return 0.0
    
    if x < 1e-10:
        return 1.0
    
    y = beta * np.sqrt(1.0 - x*x)
    return np.i0(y) / np.i0(beta)

def spiral_to_cartesian(spiral_kspace, kx_pixel_float, ky_pixel_float, image_size, kernel_width=1.0, kernel_beta=13.0):
    """Convert spiral k-space data to Cartesian grid using kernel-based gridding"""
    
    # Build weight map for each spiral point
    weight_map = []
    for i in range(len(kx_pixel_float)):
        x_center = kx_pixel_float[i]
        y_center = ky_pixel_float[i]
        local_weights = []
        
        x_min = max(0, int(x_center - kernel_width))
        x_max = min(image_size-1, int(x_center + kernel_width))
        y_min = max(0, int(y_center - kernel_width))
        y_max = min(image_size-1, int(y_center + kernel_width))
        
        for y in range(y_min, y_max+1):
            for x in range(x_min, x_max+1):
                distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
                weight = kaiser_bessel_kernel(distance / kernel_width, width=1.1, beta=kernel_beta)
                if weight > 0:
                    local_weights.append((y, x, weight))
        weight_map.append(local_weights)
    
    # Setup sparse matrix system
    n_samples = len(spiral_kspace)
    n_pixels = image_size * image_size
    
    rows, cols, vals = [], [], []
    b = np.zeros(n_samples, dtype=complex)
    
    for i, local in enumerate(weight_map):
        W = sum(w for (_,_,w) in local)
        b[i] = spiral_kspace[i] * W
        for y, x, w in local:
            idx = y * image_size + x
            rows.append(i)
            cols.append(idx)
            vals.append(w)
    
    A = spr.csr_matrix((vals, (rows, cols)), shape=(n_samples, n_pixels))
    
    # Solve with regularization
    λ = 1e-6
    sol = lsqr(A, b, damp=λ, iter_lim=10000, show=False)[0]
    
    cartesian_kspace = sol.reshape(image_size, image_size)
    
    return cartesian_kspace, weight_map

def reconstruct_with_deapod(cartesian_kspace, image_size, kernel_width=1.0, kernel_beta=13.0):
    """Reconstruct image with de-apodization correction"""
    
    # Reconstruct image
    reconstructed = np.fft.ifft2(np.fft.ifftshift(cartesian_kspace))
    
    # De-apodization
    y, x = np.indices((image_size, image_size))
    c = (image_size-1)/2
    r = np.sqrt((y-c)**2 + (x-c)**2) / kernel_width
    apod = np.i0(kernel_beta * np.sqrt(1 - np.minimum(r,1)**2)) / np.i0(kernel_beta)
    apod[r>=1] = 0
    apod /= apod.max()
    apod += 1e-6  # Avoid division by zero
    
    reconstructed_magnitude = np.abs(reconstructed) / apod
    
    return reconstructed_magnitude

# Process all sequences and create motion-corrupted data
print("\nProcessing spiral acquisition with motion simulation...")

# Use the pre-cropped short-axis volumes
volumes_to_process = cropped_short_axis_volumes

# Get actual dimensions from the cropped volumes
first_seq = list(sequences.keys())[0]
crop_size = volumes_to_process[first_seq][0].shape[1]  # Assuming cubic crop
middle_slice_idx = volumes_to_process[first_seq][0].shape[0] // 2 - 10

print(f"Using cropped volumes with size: {crop_size}x{crop_size}x{crop_size}")
print(f"Middle slice index: {middle_slice_idx}")

# Prepare pixel coordinates for spiral trajectory
kx_pixel_float = ((kx / k_max) * (crop_size/2) + crop_size/2)
ky_pixel_float = ((ky / k_max) * (crop_size/2) + crop_size/2)
kx_pixel_float = np.clip(kx_pixel_float, 0, crop_size - 1)
ky_pixel_float = np.clip(ky_pixel_float, 0, crop_size - 1)

# Store results
motion_free_images = {}
motion_corrupted_images = {}
spiral_kspace_data = {}

# Process each sequence
for seq_name in sequences.keys():
    print(f"\nProcessing {sequences[seq_name]['description']}...")
    
    motion_free_images[seq_name] = []
    motion_corrupted_images[seq_name] = []
    spiral_kspace_data[seq_name] = []
    
    # For motion simulation, we'll create a time-varying acquisition
    # Map each k-space point to a cardiac phase based on acquisition time
    readout_duration_ms = time[-1] * 1000  # Total readout time in ms
    print(f"  Readout duration: {readout_duration_ms:.2f} ms")
    
    # We have 5 cardiac frames representing different phases
    # Map the readout time to these frames
    # Even though 5 frames × 10ms = 50ms > readout time, we'll distribute
    # the frames across the readout to simulate motion
    
    # For each k-space point, determine which cardiac phase it belongs to
    # Map readout time (0 to readout_duration_ms) to frame indices (0 to 4)
    time_ms = time * 1000  # Convert to ms
    
    # Linear mapping from acquisition time to cardiac frames
    frame_indices = (time_ms / readout_duration_ms * (n_frames - 1))
    frame_indices = np.round(frame_indices).astype(int)
    frame_indices = np.clip(frame_indices, 0, n_frames - 1)
    
    print(f"  Frame distribution: {np.bincount(frame_indices)}")
    
    # Process each slice
    for slice_idx in [middle_slice_idx]:  # Process middle slice for demonstration
        print(f"  Processing slice {slice_idx}...")
        
        # First, get k-space data for all time frames at this slice
        kspace_all_frames = []
        for frame_idx in range(n_frames):
            # Get the image - already cropped
            volume = volumes_to_process[seq_name][frame_idx]
            img_slice = volume[slice_idx, :, :]
            
            # Verify size matches expected crop_size
            if img_slice.shape[0] != crop_size or img_slice.shape[1] != crop_size:
                print(f"    Warning: Slice shape {img_slice.shape} doesn't match crop_size {crop_size}")
                # Center crop if needed
                center_y, center_x = img_slice.shape[0]//2, img_slice.shape[1]//2
                img_slice = img_slice[center_y-crop_size//2:center_y+crop_size//2,
                                     center_x-crop_size//2:center_x+crop_size//2]
            
            # Compute k-space
            kspace = np.fft.fftshift(np.fft.fft2(img_slice))
            kspace_all_frames.append(kspace)
        
        # 1. Motion-free reconstruction (use frame 0 only)
        print("    Creating motion-free reconstruction...")
        kspace_ref = kspace_all_frames[0]
        
        # Sample at spiral locations
        spiral_kspace_ref = np.zeros(len(kx_pixel_float), dtype=complex)
        kernel_width = 1.0
        kernel_beta = 13.0
        
        for i in range(len(kx_pixel_float)):
            x_center = kx_pixel_float[i]
            y_center = ky_pixel_float[i]
            
            x_min = max(0, int(x_center - kernel_width))
            x_max = min(crop_size-1, int(x_center + kernel_width))
            y_min = max(0, int(y_center - kernel_width))
            y_max = min(crop_size-1, int(y_center + kernel_width))
            
            weighted_sum = 0.0j
            total_weight = 0.0
            
            for y in range(y_min, y_max+1):
                for x in range(x_min, x_max+1):
                    distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
                    weight = kaiser_bessel_kernel(distance / kernel_width, width=1.1, beta=kernel_beta)
                    if weight > 0:
                        weighted_sum += weight * kspace_ref[y, x]
                        total_weight += weight
            
            if total_weight > 0:
                spiral_kspace_ref[i] = weighted_sum / total_weight
        
        # Grid to Cartesian and reconstruct
        cartesian_kspace_ref, _ = spiral_to_cartesian(spiral_kspace_ref, kx_pixel_float, 
                                                      ky_pixel_float, crop_size)
        motion_free_img = reconstruct_with_deapod(cartesian_kspace_ref, crop_size)
        motion_free_images[seq_name].append(motion_free_img)
        
        # 2. Motion-corrupted reconstruction
        print("    Creating motion-corrupted reconstruction...")
        spiral_kspace_motion = np.zeros(len(kx_pixel_float), dtype=complex)
        
        # Sample from different frames based on acquisition time
        for i in range(len(kx_pixel_float)):
            # Get the frame index for this k-space point
            frame_for_this_point = frame_indices[i]
            kspace_frame = kspace_all_frames[frame_for_this_point]
            
            x_center = kx_pixel_float[i]
            y_center = ky_pixel_float[i]
            
            x_min = max(0, int(x_center - kernel_width))
            x_max = min(crop_size-1, int(x_center + kernel_width))
            y_min = max(0, int(y_center - kernel_width))
            y_max = min(crop_size-1, int(y_center + kernel_width))
            
            weighted_sum = 0.0j
            total_weight = 0.0
            
            for y in range(y_min, y_max+1):
                for x in range(x_min, x_max+1):
                    distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
                    weight = kaiser_bessel_kernel(distance / kernel_width, width=1.1, beta=kernel_beta)
                    if weight > 0:
                        weighted_sum += weight * kspace_frame[y, x]
                        total_weight += weight
            
            if total_weight > 0:
                spiral_kspace_motion[i] = weighted_sum / total_weight
        
        # Grid to Cartesian and reconstruct
        cartesian_kspace_motion, _ = spiral_to_cartesian(spiral_kspace_motion, kx_pixel_float, 
                                                         ky_pixel_float, crop_size)
        motion_corrupted_img = reconstruct_with_deapod(cartesian_kspace_motion, crop_size)
        motion_corrupted_images[seq_name].append(motion_corrupted_img)
        
        # Store spiral k-space data
        spiral_kspace_data[seq_name].append({
            'motion_free': spiral_kspace_ref,
            'motion_corrupted': spiral_kspace_motion,
            'frame_indices': frame_indices
        })

# Visualize results
print("\nVisualizing motion artifacts...")

# Plot comparison for each sequence
for seq_idx, (seq_name, seq_params) in enumerate(sequences.items()):
    plt.figure(figsize=(15, 10))
    plt.suptitle(f'{seq_params["description"]} - Motion Artifact Simulation', fontsize=16)
    
    # Get images
    motion_free = motion_free_images[seq_name][0]
    motion_corrupted = motion_corrupted_images[seq_name][0]
    difference = np.abs(motion_corrupted - motion_free)
    
    # Row 1: Images
    plt.subplot(2, 3, 1)
    plt.imshow(motion_free, cmap='gray')
    plt.title('Motion-Free')
    plt.colorbar(fraction=0.046)
    plt.axis('off')
    
    plt.subplot(2, 3, 2)
    plt.imshow(motion_corrupted, cmap='gray')
    plt.title('With Motion Artifacts')
    plt.colorbar(fraction=0.046)
    plt.axis('off')
    
    plt.subplot(2, 3, 3)
    plt.imshow(difference, cmap='hot')
    plt.title('Absolute Difference')
    plt.colorbar(fraction=0.046)
    plt.axis('off')
    
    # Row 2: K-space and acquisition timeline
    plt.subplot(2, 3, 4)
    # Show spiral trajectory colored by acquisition time
    scatter = plt.scatter(kx_pixel_float - crop_size/2, ky_pixel_float - crop_size/2, 
                         c=time*1000, s=1, cmap='viridis')
    plt.colorbar(scatter, label='Time (ms)')
    plt.title('Spiral Trajectory (colored by time)')
    plt.axis('equal')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 3, 5)
    # Show which frame each k-space point came from
    frame_data = spiral_kspace_data[seq_name][0]['frame_indices']
    scatter2 = plt.scatter(kx_pixel_float - crop_size/2, ky_pixel_float - crop_size/2, 
                          c=frame_data, s=1, cmap='tab10', vmin=0, vmax=n_frames-1)
    plt.colorbar(scatter2, label='Frame Index', ticks=range(n_frames))
    plt.title('K-space Points by Source Frame')
    plt.axis('equal')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 3, 6)
    # Plot profile through center
    center_profile_free = motion_free[crop_size//2, :]
    center_profile_motion = motion_corrupted[crop_size//2, :]
    plt.plot(center_profile_free, 'b-', label='Motion-free', linewidth=2)
    plt.plot(center_profile_motion, 'r--', label='With motion', linewidth=2)
    plt.title('Horizontal Profile Through Center')
    plt.xlabel('Pixel')
    plt.ylabel('Intensity')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Create an animation showing how k-space is filled over time
print("\nCreating k-space filling visualization...")

seq_to_animate = 'T2w_SE'

# Note: n_animation_frames is for visualizing the k-space filling process,
# NOT the number of cardiac frames. We're showing how k-space is filled
# progressively during the spiral readout
n_animation_frames = 10  # Reduced for clearer visualization
points_per_frame = len(kx_pixel_float) // n_animation_frames

# Create static visualization of k-space filling at key time points
# These are key points during the readout, NOT cardiac frames
key_time_points = [0, 2, 4, 6, 8]  # Animation frames showing readout progress
fig = plt.figure(figsize=(20, 4))
fig.suptitle('K-space Acquisition Progress (colored by cardiac frame source)', fontsize=14)

axes = []
for i, anim_frame in enumerate(key_time_points):
    ax = plt.subplot(1, len(key_time_points), i+1)
    axes.append(ax)
    
    end_idx = min((anim_frame + 1) * points_per_frame, len(kx_pixel_float))
    
    # Show partial trajectory colored by cardiac frame source
    if end_idx > 0:
        scatter = ax.scatter(kx_pixel_float[:end_idx] - crop_size/2, 
                            ky_pixel_float[:end_idx] - crop_size/2, 
                            c=frame_indices[:end_idx], s=1, cmap='tab10', 
                            vmin=0, vmax=n_frames-1)
    
    # Show remaining trajectory in gray
    if end_idx < len(kx_pixel_float):
        ax.scatter(kx_pixel_float[end_idx:] - crop_size/2, 
                  ky_pixel_float[end_idx:] - crop_size/2, 
                  c='lightgray', s=0.5, alpha=0.3)
    
    # Add time and progress info
    current_time_ms = time[end_idx-1]*1000 if end_idx > 0 else 0
    pct_complete = (end_idx / len(kx_pixel_float)) * 100
    ax.set_title(f't = {current_time_ms:.1f} ms\n({pct_complete:.0f}% complete)')
    ax.axis('equal')
    ax.set_xlim(-crop_size/2, crop_size/2)
    ax.set_ylim(-crop_size/2, crop_size/2)
    ax.grid(True, alpha=0.3)

# Add colorbar to the last subplot only
if 'scatter' in locals():
    cbar = plt.colorbar(scatter, ax=axes[-1], label='Cardiac Frame', 
                       ticks=range(n_frames), fraction=0.046, pad=0.04)

plt.tight_layout()
plt.show()

# Create a separate figure for animated k-space filling
fig2, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
fig2.suptitle(f'{sequences[seq_to_animate]["description"]} - K-space Filling Example', fontsize=14)

# Show the complete k-space trajectory
ax1.set_title('Complete K-space Trajectory')
scatter_full = ax1.scatter(kx_pixel_float - crop_size/2, 
                          ky_pixel_float - crop_size/2, 
                          c=frame_indices, s=1, cmap='tab10', 
                          vmin=0, vmax=n_frames-1)
ax1.set_xlabel('kx')
ax1.set_ylabel('ky')
ax1.axis('equal')
ax1.grid(True, alpha=0.3)
ax1.set_xlim(-crop_size/2, crop_size/2)
ax1.set_ylim(-crop_size/2, crop_size/2)

# Add colorbar for cardiac frames
cbar2 = plt.colorbar(scatter_full, ax=ax1, label='Cardiac Frame', 
                    ticks=range(n_frames), fraction=0.046, pad=0.04)

# Show the final reconstructed image
ax2.set_title('Final Motion-Corrupted Image')
final_img = motion_corrupted_images[seq_to_animate][0]
im = ax2.imshow(final_img, cmap='gray')
ax2.axis('off')
plt.colorbar(im, ax=ax2, fraction=0.046, pad=0.04)

plt.tight_layout()
plt.show()

# Quantitative analysis
print("\nQuantitative Motion Artifact Analysis:")
print("-" * 60)

for seq_name in sequences.keys():
    motion_free = motion_free_images[seq_name][0]
    motion_corrupted = motion_corrupted_images[seq_name][0]
    
    # Calculate metrics
    mse = np.mean((motion_free - motion_corrupted)**2)
    psnr = 20 * np.log10(np.max(motion_free) / np.sqrt(mse + 1e-12))
    ssim_approx = np.mean(motion_free * motion_corrupted) / (np.std(motion_free) * np.std(motion_corrupted))
    
    print(f"\n{sequences[seq_name]['description']}:")
    print(f"  MSE: {mse:.4e}")
    print(f"  PSNR: {psnr:.2f} dB")
    print(f"  Correlation: {ssim_approx:.4f}")
    
    # Analyze artifact patterns
    artifact_map = np.abs(motion_corrupted - motion_free)
    max_artifact = np.max(artifact_map)
    mean_artifact = np.mean(artifact_map)
    
    print(f"  Max artifact intensity: {max_artifact:.4f}")
    print(f"  Mean artifact intensity: {mean_artifact:.4f}")
    print(f"  Artifact ratio: {mean_artifact/np.mean(motion_free)*100:.2f}%")

# Visualize cardiac frame distribution across spiral readout
plt.figure(figsize=(12, 4))
plt.suptitle('Cardiac Frame Distribution Across Spiral Readout', fontsize=14)

plt.subplot(1, 2, 1)
# Histogram of frame usage
frame_counts = np.bincount(frame_indices)
plt.bar(range(len(frame_counts)), frame_counts)
plt.xlabel('Cardiac Frame Index')
plt.ylabel('Number of k-space points')
plt.title('K-space Points per Cardiac Frame')
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
# Show temporal distribution
plt.plot(time * 1000, frame_indices, 'b-', linewidth=2)
plt.xlabel('Acquisition Time (ms)')
plt.ylabel('Cardiac Frame Index')
plt.title('Cardiac Frame vs Acquisition Time')
plt.grid(True, alpha=0.3)
plt.ylim(-0.5, n_frames - 0.5)
plt.yticks(range(n_frames))

# Add shaded regions to show frame boundaries
colors = plt.cm.tab10(np.linspace(0, 1, n_frames))
for i in range(n_frames):
    frame_mask = frame_indices == i
    if np.any(frame_mask):
        t_start = time[frame_mask][0] * 1000
        t_end = time[frame_mask][-1] * 1000
        plt.axhspan(i - 0.4, i + 0.4, xmin=t_start/plt.xlim()[1], 
                   xmax=t_end/plt.xlim()[1], alpha=0.3, color=colors[i])

plt.tight_layout()
plt.show()

print("\nMotion artifact simulation complete!")
print(f"Total spiral readout points: {len(k)}")
print(f"Readout duration: {time[-1]*1000:.2f} ms")
print(f"Cardiac frames used: {n_frames}")
print("Each k-space point was sampled from the cardiac frame corresponding to its acquisition time.")

import numpy as np
import matplotlib.pyplot as plt

# Cartesian Motion Artifact Simulation
print("Creating Cartesian motion artifact simulation...")
print("=" * 70)

# Use the pre-cropped short-axis volumes
volumes_to_process = cropped_short_axis_volumes

# Get actual dimensions from the cropped volumes
first_seq = list(sequences.keys())[0]
crop_size = volumes_to_process[first_seq][0].shape[1]  # Assuming cubic crop
middle_slice_idx = volumes_to_process[first_seq][0].shape[0] // 2 - 10

print(f"Using cropped volumes with size: {crop_size}x{crop_size}x{crop_size}")
print(f"Middle slice index: {middle_slice_idx}")
print(f"Number of cardiac frames: {n_frames}")

# Store results
cartesian_motion_free_images = {}
cartesian_motion_corrupted_images = {}
cartesian_kspace_data = {}

# Process each sequence
for seq_name in sequences.keys():
    print(f"\nProcessing {sequences[seq_name]['description']} with Cartesian sampling...")
    
    cartesian_motion_free_images[seq_name] = []
    cartesian_motion_corrupted_images[seq_name] = []
    cartesian_kspace_data[seq_name] = []
    
    # Process the middle slice
    slice_idx = middle_slice_idx
    print(f"  Processing slice {slice_idx}...")
    
    # Get k-space data for all time frames at this slice
    kspace_all_frames = []
    images_all_frames = []
    
    for frame_idx in range(n_frames):
        # Get the image - already cropped
        volume = volumes_to_process[seq_name][frame_idx]
        img_slice = volume[slice_idx, :, :]
        
        # Verify size matches expected crop_size
        if img_slice.shape[0] != crop_size or img_slice.shape[1] != crop_size:
            print(f"    Warning: Slice shape {img_slice.shape} doesn't match crop_size {crop_size}")
            # Center crop if needed
            center_y, center_x = img_slice.shape[0]//2, img_slice.shape[1]//2
            img_slice = img_slice[center_y-crop_size//2:center_y+crop_size//2,
                                 center_x-crop_size//2:center_x+crop_size//2]
        
        images_all_frames.append(img_slice)
        
        # Compute k-space (FFT-shifted for visualization)
        kspace = np.fft.fftshift(np.fft.fft2(img_slice))
        kspace_all_frames.append(kspace)
    
    # 1. Motion-free reconstruction (use frame 0 only)
    print("    Creating motion-free reconstruction...")
    kspace_ref = kspace_all_frames[0]
    
    # Simple inverse FFT reconstruction
    motion_free_img = np.abs(np.fft.ifft2(np.fft.ifftshift(kspace_ref)))
    cartesian_motion_free_images[seq_name].append(motion_free_img)
    
    # 2. Motion-corrupted reconstruction
    print("    Creating motion-corrupted reconstruction...")
    
    # Create motion-corrupted k-space by replacing horizontal strips
    # Divide k-space into n_frames horizontal strips (top to bottom)
    kspace_motion_corrupted = kspace_ref.copy()
    
    # Calculate strip boundaries
    strip_height = crop_size // n_frames
    frame_assignments = []
    
    for frame_idx in range(n_frames):
        start_row = frame_idx * strip_height
        
        # Handle the last strip to include any remaining rows
        if frame_idx == n_frames - 1:
            end_row = crop_size
        else:
            end_row = (frame_idx + 1) * strip_height
        
        # Replace this strip with k-space data from the corresponding frame
        kspace_motion_corrupted[start_row:end_row, :] = kspace_all_frames[frame_idx][start_row:end_row, :]
        
        frame_assignments.append((start_row, end_row, frame_idx))
        print(f"      Strip {frame_idx}: rows {start_row}-{end_row-1} from frame {frame_idx}")
    
    # Reconstruct motion-corrupted image
    motion_corrupted_img = np.abs(np.fft.ifft2(np.fft.ifftshift(kspace_motion_corrupted)))
    cartesian_motion_corrupted_images[seq_name].append(motion_corrupted_img)
    
    # Store k-space data for visualization
    cartesian_kspace_data[seq_name].append({
        'motion_free_kspace': kspace_ref,
        'motion_corrupted_kspace': kspace_motion_corrupted,
        'all_frames_kspace': kspace_all_frames,
        'all_frames_images': images_all_frames,
        'frame_assignments': frame_assignments
    })

# Visualize results
print("\nVisualizing Cartesian motion artifacts...")

# Plot comparison for each sequence
for seq_idx, (seq_name, seq_params) in enumerate(sequences.items()):
    fig = plt.figure(figsize=(18, 12))
    fig.suptitle(f'{seq_params["description"]} - Cartesian Motion Artifact Simulation', fontsize=16)
    
    # Get images and k-space data
    motion_free = cartesian_motion_free_images[seq_name][0]
    motion_corrupted = cartesian_motion_corrupted_images[seq_name][0]
    difference = np.abs(motion_corrupted - motion_free)
    
    kspace_data = cartesian_kspace_data[seq_name][0]
    kspace_ref = kspace_data['motion_free_kspace']
    kspace_corrupted = kspace_data['motion_corrupted_kspace']
    frame_assignments = kspace_data['frame_assignments']
    
    # Row 1: Images
    plt.subplot(3, 4, 1)
    plt.imshow(motion_free, cmap='gray')
    plt.title('Motion-Free Image')
    plt.colorbar(fraction=0.046)
    plt.axis('off')
    
    plt.subplot(3, 4, 2)
    plt.imshow(motion_corrupted, cmap='gray')
    plt.title('Motion-Corrupted Image')
    plt.colorbar(fraction=0.046)
    plt.axis('off')
    
    plt.subplot(3, 4, 3)
    plt.imshow(difference, cmap='hot')
    plt.title('Absolute Difference')
    plt.colorbar(fraction=0.046)
    plt.axis('off')
    
    plt.subplot(3, 4, 4)
    # Profile comparison
    center_profile_free = motion_free[crop_size//2, :]
    center_profile_motion = motion_corrupted[crop_size//2, :]
    plt.plot(center_profile_free, 'b-', label='Motion-free', linewidth=2)
    plt.plot(center_profile_motion, 'r--', label='With motion', linewidth=2)
    plt.title('Horizontal Profile Through Center')
    plt.xlabel('Pixel')
    plt.ylabel('Intensity')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Row 2: K-space magnitude
    plt.subplot(3, 4, 5)
    plt.imshow(np.log(np.abs(kspace_ref) + 1), cmap='gray')
    plt.title('Motion-Free K-space\n(log magnitude)')
    plt.colorbar(fraction=0.046)
    plt.axis('off')
    
    plt.subplot(3, 4, 6)
    plt.imshow(np.log(np.abs(kspace_corrupted) + 1), cmap='gray')
    plt.title('Motion-Corrupted K-space\n(log magnitude)')
    plt.colorbar(fraction=0.046)
    plt.axis('off')
    
    plt.subplot(3, 4, 7)
    kspace_diff = np.abs(kspace_corrupted - kspace_ref)
    plt.imshow(np.log(kspace_diff + 1), cmap='hot')
    plt.title('K-space Difference\n(log magnitude)')
    plt.colorbar(fraction=0.046)
    plt.axis('off')
    
    plt.subplot(3, 4, 8)
    # Show k-space strip assignments
    strip_map = np.zeros((crop_size, crop_size))
    colors = plt.cm.tab10(np.linspace(0, 1, n_frames))
    
    for start_row, end_row, frame_idx in frame_assignments:
        strip_map[start_row:end_row, :] = frame_idx
    
    im = plt.imshow(strip_map, cmap='tab10', vmin=0, vmax=n_frames-1)
    plt.title('K-space Strip Assignment\nby Cardiac Frame')
    cbar = plt.colorbar(im, fraction=0.046, ticks=range(n_frames))
    cbar.set_label('Frame Index')
    plt.axis('off')
    
    # Row 3: Show individual cardiac frames
    for frame_idx in range(min(4, n_frames)):
        plt.subplot(3, 4, 9 + frame_idx)
        frame_img = kspace_data['all_frames_images'][frame_idx]
        plt.imshow(frame_img, cmap='gray')
        plt.title(f'Cardiac Frame {frame_idx}')
        plt.axis('off')
        if frame_idx < 3:  # Only add colorbar to first 3 to save space
            plt.colorbar(fraction=0.046)
    
    plt.tight_layout()
    plt.show()

# Create animation-style visualization showing k-space filling
print("\nCreating k-space filling animation for Cartesian sampling...")

seq_to_animate = 'T2w_SE'
kspace_data = cartesian_kspace_data[seq_to_animate][0]

# Show progressive k-space filling
fig = plt.figure(figsize=(20, 6))
fig.suptitle(f'{sequences[seq_to_animate]["description"]} - Cartesian K-space Progressive Filling', fontsize=14)

n_steps = n_frames + 1  # Include the final complete image
for step in range(n_steps):
    ax = plt.subplot(2, n_steps, step + 1)
    
    if step == 0:
        # Show motion-free k-space
        kspace_display = kspace_data['motion_free_kspace']
        title = 'Motion-Free\nK-space'
    else:
        # Show progressive motion-corrupted k-space filling
        kspace_display = kspace_data['motion_free_kspace'].copy()
        
        # Fill strips up to current step
        for fill_step in range(step):
            if fill_step < len(kspace_data['frame_assignments']):
                start_row, end_row, frame_idx = kspace_data['frame_assignments'][fill_step]
                source_kspace = kspace_data['all_frames_kspace'][frame_idx]
                kspace_display[start_row:end_row, :] = source_kspace[start_row:end_row, :]
        
        title = f'Step {step}: Added\nFrame {step-1} Strip'
    
    # Show k-space
    im = ax.imshow(np.log(np.abs(kspace_display) + 1), cmap='gray')
    ax.set_title(title)
    ax.axis('off')
    
    # Add colored overlay to show which strips are filled
    if step > 0:
        overlay = np.zeros((crop_size, crop_size, 4))
        colors = plt.cm.tab10(np.linspace(0, 1, n_frames))
        
        for fill_step in range(step):
            if fill_step < len(kspace_data['frame_assignments']):
                start_row, end_row, frame_idx = kspace_data['frame_assignments'][fill_step]
                overlay[start_row:end_row, :, :3] = colors[frame_idx][:3]
                overlay[start_row:end_row, :, 3] = 0.3  # Alpha
        
        ax.imshow(overlay)
    
    # Show corresponding reconstructed image
    ax2 = plt.subplot(2, n_steps, step + 1 + n_steps)
    recon_img = np.abs(np.fft.ifft2(np.fft.ifftshift(kspace_display)))
    ax2.imshow(recon_img, cmap='gray')
    ax2.set_title('Reconstructed\nImage')
    ax2.axis('off')

plt.tight_layout()
plt.show()

# Quantitative analysis
print("\nQuantitative Cartesian Motion Artifact Analysis:")
print("-" * 60)

for seq_name in sequences.keys():
    motion_free = cartesian_motion_free_images[seq_name][0]
    motion_corrupted = cartesian_motion_corrupted_images[seq_name][0]
    
    # Calculate metrics
    mse = np.mean((motion_free - motion_corrupted)**2)
    rmse = np.sqrt(mse)
    psnr = 20 * np.log10(np.max(motion_free) / (rmse + 1e-12))
    
    # Normalized cross-correlation
    ncc = np.corrcoef(motion_free.flatten(), motion_corrupted.flatten())[0, 1]
    
    # Structural similarity approximation
    mean_free = np.mean(motion_free)
    mean_corrupted = np.mean(motion_corrupted)
    var_free = np.var(motion_free)
    var_corrupted = np.var(motion_corrupted)
    covar = np.mean((motion_free - mean_free) * (motion_corrupted - mean_corrupted))
    
    c1 = (0.01 * np.max(motion_free))**2
    c2 = (0.03 * np.max(motion_free))**2
    ssim_approx = ((2*mean_free*mean_corrupted + c1) * (2*covar + c2)) / \
                  ((mean_free**2 + mean_corrupted**2 + c1) * (var_free + var_corrupted + c2))
    
    print(f"\n{sequences[seq_name]['description']}:")
    print(f"  MSE: {mse:.4e}")
    print(f"  RMSE: {rmse:.4e}")
    print(f"  PSNR: {psnr:.2f} dB")
    print(f"  Normalized Cross-Correlation: {ncc:.4f}")
    print(f"  SSIM (approx): {ssim_approx:.4f}")
    
    # Analyze artifact patterns
    artifact_map = np.abs(motion_corrupted - motion_free)
    max_artifact = np.max(artifact_map)
    mean_artifact = np.mean(artifact_map)
    
    print(f"  Max artifact intensity: {max_artifact:.4f}")
    print(f"  Mean artifact intensity: {mean_artifact:.4f}")
    print(f"  Artifact ratio: {mean_artifact/np.mean(motion_free)*100:.2f}%")

# Compare Cartesian vs Spiral artifacts side by side
print("\nComparing Cartesian vs Spiral Motion Artifacts...")

comparison_seq = 'T2w_SE'
if comparison_seq in motion_free_images and comparison_seq in cartesian_motion_free_images:
    
    plt.figure(figsize=(16, 8))
    plt.suptitle(f'Cartesian vs Spiral Motion Artifacts - {sequences[comparison_seq]["description"]}', fontsize=16)
    
    # Get images
    spiral_free = motion_free_images[comparison_seq][0]
    spiral_corrupted = motion_corrupted_images[comparison_seq][0]
    cartesian_free = cartesian_motion_free_images[comparison_seq][0]
    cartesian_corrupted = cartesian_motion_corrupted_images[comparison_seq][0]
    
    # Row 1: Motion-free images
    plt.subplot(2, 4, 1)
    plt.imshow(spiral_free, cmap='gray')
    plt.title('Spiral: Motion-Free')
    plt.axis('off')
    plt.colorbar(fraction=0.046)
    
    plt.subplot(2, 4, 2)
    plt.imshow(spiral_corrupted, cmap='gray')
    plt.title('Spiral: Motion-Corrupted')
    plt.axis('off')
    plt.colorbar(fraction=0.046)
    
    plt.subplot(2, 4, 3)
    plt.imshow(cartesian_free, cmap='gray')
    plt.title('Cartesian: Motion-Free')
    plt.axis('off')
    plt.colorbar(fraction=0.046)
    
    plt.subplot(2, 4, 4)
    plt.imshow(cartesian_corrupted, cmap='gray')
    plt.title('Cartesian: Motion-Corrupted')
    plt.axis('off')
    plt.colorbar(fraction=0.046)
    
    # Row 2: Difference images
    plt.subplot(2, 4, 5)
    spiral_diff = np.abs(spiral_corrupted - spiral_free)
    plt.imshow(spiral_diff, cmap='hot')
    plt.title('Spiral: Artifacts')
    plt.axis('off')
    plt.colorbar(fraction=0.046)
    
    plt.subplot(2, 4, 6)
    cartesian_diff = np.abs(cartesian_corrupted - cartesian_free)
    plt.imshow(cartesian_diff, cmap='hot')
    plt.title('Cartesian: Artifacts')
    plt.axis('off')
    plt.colorbar(fraction=0.046)
    
    # Profile comparisons
    plt.subplot(2, 4, 7)
    center_row = crop_size // 2
    plt.plot(spiral_free[center_row, :], 'b-', label='Motion-free', linewidth=2)
    plt.plot(spiral_corrupted[center_row, :], 'r--', label='Motion-corrupted', linewidth=2)
    plt.title('Spiral: Center Profile')
    plt.xlabel('Pixel')
    plt.ylabel('Intensity')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 4, 8)
    plt.plot(cartesian_free[center_row, :], 'b-', label='Motion-free', linewidth=2)
    plt.plot(cartesian_corrupted[center_row, :], 'r--', label='Motion-corrupted', linewidth=2)
    plt.title('Cartesian: Center Profile')
    plt.xlabel('Pixel')
    plt.ylabel('Intensity')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

print("\nCartesian motion artifact simulation complete!")
print(f"Image size: {crop_size}x{crop_size}")
print(f"Cardiac frames used: {n_frames}")
print("K-space was divided into horizontal strips, each filled with data from different cardiac frames.")

# 1. Transform parameter maps to short-axis view
param_maps_short_axis = {}

# print("\nTransforming parameter maps to short-axis view...")
# Check the type of frame_data[0] first
# print(f"Type of frame_data[0]: {type(frame_data[0])}")

# Reload the first frame data to ensure we have the correct object
frame_path = os.path.join(base_path, f"frame_{1:02d}", "cardiac_region_new", "cardiac_volume.npz")
first_frame_data = np.load(frame_path)
print(f"Available keys in first_frame_data: {list(first_frame_data.keys())}")


for param_name in ['T2star', 'T2star_plus', 'B0', 'ShimmedB0', 'T2','Labels']:
    if param_name in first_frame_data:
        print(f"  Processing {param_name} map...")
        # Get the parameter map and transpose to (Z, Y, X) order
        param_map = first_frame_data[param_name]
        param_map_transposed = np.transpose(param_map, [2, 0, 1])  # From (Y, X, Z) to (Z, Y, X)
        
        # Apply the same three transformations sequentially
        current_param_map = param_map_transposed
        best_slice = param_map_transposed.shape[0] // 2  # Initial middle slice
        
        for i, transform in enumerate(transformations):
            if i > 0:  # Update best_slice for subsequent transformations
                best_slice = current_param_map.shape[0] // 2 + transformations[i-1]['offset']
            
            current_param_map = create_oblique_3D_volume(
                current_param_map, 
                best_slice, 
                Lu=Lu, 
                Lv=Lv, 
                Lw=Lw, 
                theta=transform['theta']
            )
        
        # Store the transformed parameter map in format compatible with the cropping function
        param_maps_short_axis[param_name] = [current_param_map]  # Put in a list as if it's one frame
        print(f"    {param_name} transformed: shape {current_param_map.shape}")

# 2. Crop the parameter maps
print("\nCropping parameter maps...")
cropped_param_maps = crop_short_axis_volumes(
    short_axis_volumes=param_maps_short_axis,
    crop_size=crop_size  # Use the same crop_size
)

# 3. Create final dictionary with the cropped parameter maps
param_volume_transformed = {}
for param_name in cropped_param_maps:
    # Extract the first (and only) frame from each parameter map
    param_volume_transformed[param_name] = cropped_param_maps[param_name][0]
    print(f"  {param_name} final shape: {param_volume_transformed[param_name].shape}")

cardiac_labels = [1, 3, 4, 5, 6, 7, 8] #include pericardial region
# Initialize the cardiac mask
crop_cardiac_mask = np.zeros_like(param_volume_transformed['Labels'], dtype=bool)

for label in cardiac_labels:
    crop_cardiac_mask |= (param_volume_transformed['Labels'] == label)
        
param_volume_transformed['B0'] = param_volume_transformed['B0'] - np.median(param_volume_transformed['B0'][crop_cardiac_mask])

# Visualize short-axis views for all sequences (Frame 1)
frame_to_show = 0
sa_slice_idx = Lw // 2 - 10  # Middle slice of short-axis volume

z_slice = first_frame_data['Labels'].shape[2] // 2

plt.figure(figsize=(18, 12))
plt.suptitle(f'Short-Axis Views - Frame {frame_to_show + 1}, Slice {sa_slice_idx}', fontsize=16)

# Show original axial views (top row) and short-axis views (bottom row)
for i, param_name in enumerate(param_maps_short_axis.keys()):
    # Original axial view
    plt.subplot(2, len(param_maps_short_axis), i + 1)
    axial_slice = first_frame_data[param_name][:, :, z_slice]
    plt.imshow(axial_slice, cmap='gray')
    plt.title(f'{param_name}\n(Axial)', fontsize=10)
    plt.axis('off')
    
    # Short-axis view
    plt.subplot(2, len(param_maps_short_axis), i + 1 + len(param_maps_short_axis))
    sa_slice = param_maps_short_axis[param_name][0][sa_slice_idx, :, :]
    plt.imshow(sa_slice, cmap='gray')
    plt.title(f'{param_name}\n(Short-Axis)', fontsize=10)
    plt.axis('off')

plt.tight_layout()
plt.show()

import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata, NearestNDInterpolator
from scipy.ndimage import gaussian_filter
from scipy import special
import scipy.sparse as spr
from scipy.sparse.linalg import lsqr

# Function to simulate T2* decay and B0 effects during spiral acquisition
def simulate_spiral_t2star_b0_effects(kspace_cartesian, t2star_map, b0_map, 
                                     kx_pixel_float, ky_pixel_float, 
                                     sampling_times, weight_map, image_size):
    """
    Simulate T2* decay and B0 effects in spiral MRI acquisition
    
    Parameters:
    -----------
    kspace_cartesian: 2D complex array - Cartesian k-space data
    t2star_map: 2D array - T2* values in milliseconds
    b0_map: 2D array - B0 field inhomogeneity map in Hz
    kx_pixel_float, ky_pixel_float: Spiral trajectory coordinates
    sampling_times: Acquisition time for each k-space point
    weight_map: Weights for spiral-to-Cartesian gridding
    image_size: Size of the image
    
    Returns:
    --------
    spiral_kspace_with_effects: Complex array with T2* and B0 effects
    """
    
    # Convert to image domain
    orig_image = np.fft.ifft2(np.fft.ifftshift(kspace_cartesian))
    
    # Convert T2* map from ms to seconds
    t2star_sec = t2star_map / 1000.0
    
    # Avoid too small T2* values
    min_t2s = 5.0 / 1000.0  # 5 ms minimum
    t2star_sec = np.maximum(t2star_sec, min_t2s)
    
    # Initialize output
    spiral_kspace_with_effects = np.zeros(len(kx_pixel_float), dtype=complex)
    
    # Use time binning for efficient processing
    time_bin = 0.001  # 1ms bins
    time_bins = np.arange(0, np.max(sampling_times) + time_bin, time_bin)
    
    # Process each time bin
    for i, t in enumerate(time_bins[:-1]):
        # Find points in this time bin
        time_indices = np.where((sampling_times >= t) & (sampling_times < time_bins[i+1]))[0]
        
        if len(time_indices) == 0:
            continue
        
        # Calculate T2* decay at this time
        t_mid = (t + time_bins[i+1]) / 2  # Use middle of time bin
        decay = np.exp(-t_mid / t2star_sec)
        
        # Calculate phase from B0 field
        b0_phase = np.exp(-1j * 2 * np.pi * b0_map * t_mid)
        
        # Apply both effects to the original image
        decayed_image = orig_image * decay * b0_phase
        
        # Convert back to k-space
        decayed_kspace = np.fft.fftshift(np.fft.fft2(decayed_image))
        
        # Sample at spiral trajectory points for this time bin using weight map
        for idx in time_indices:
            value = 0.0j
            total_weight = 0.0
            for y, x, weight in weight_map[idx]:
                if 0 <= y < image_size and 0 <= x < image_size:
                    value += weight * decayed_kspace[y, x]
                    total_weight += weight
            if total_weight > 0:
                spiral_kspace_with_effects[idx] = value / total_weight
            else:
                spiral_kspace_with_effects[idx] = 0.0
    
    return spiral_kspace_with_effects

## Process all sequences with T2* decay and B0 effects
print("\nProcessing spiral acquisition with motion, T2* decay, and B0 effects...")
print("=" * 70)

# Store results with effects
motion_free_with_effects = {}
motion_corrupted_with_effects = {}
motion_corrupted_with_shimmed_b0 = {}

# Process each sequence
for seq_name in sequences.keys():
    print(f"\nProcessing {sequences[seq_name]['description']}...")
    
    motion_free_with_effects[seq_name] = []
    motion_corrupted_with_effects[seq_name] = []
    motion_corrupted_with_shimmed_b0[seq_name] = []
    
    # Get the parameter maps (already cropped)
    param_volume = param_volume_transformed
    
    # Process multiple slices
    slice_indices = [middle_slice_idx - 2, middle_slice_idx, middle_slice_idx + 2]
    
    for slice_idx in slice_indices:
        print(f"  Processing slice {slice_idx}...")
        
        print(f"param_volume type: {type(param_volume)}")
        print(f"param_volume keys: {list(param_volume.keys()) if isinstance(param_volume, dict) else 'Not a dict'}")
        
        # Get parameter maps for this slice (CORRECTED INDEXING)
        t2star_slice = param_volume['T2star_plus'][slice_idx, :, :] if 'T2star_plus' in param_volume else param_volume['T2star'][slice_idx, :, :]
        b0_slice = param_volume['B0'][slice_idx, :, :] if 'B0' in param_volume else np.zeros((crop_size, crop_size))
        shimmed_b0_slice = param_volume['ShimmedB0'][slice_idx, :, :] if 'ShimmedB0' in param_volume else b0_slice
        
        # Get k-space data for all time frames at this slice
        kspace_all_frames = []
        for frame_idx in range(n_frames):
            volume = volumes_to_process[seq_name][frame_idx]
            img_slice = volume[slice_idx, :, :]  # This indexing is correct
            
            if img_slice.shape[0] != crop_size or img_slice.shape[1] != crop_size:
                center_y, center_x = img_slice.shape[0]//2, img_slice.shape[1]//2
                img_slice = img_slice[center_y-crop_size//2:center_y+crop_size//2,
                                     center_x-crop_size//2:center_x+crop_size//2]
            
            kspace = np.fft.fftshift(np.fft.fft2(img_slice))
            kspace_all_frames.append(kspace)
        
        # 1. Motion-free with T2* and B0 effects (use frame 0 only)
        print("    Creating motion-free reconstruction with T2* and B0...")
        kspace_ref = kspace_all_frames[0]
        
        # Build weight map once for this slice
        weight_map = []
        kernel_width = 1.0
        kernel_beta = 13.0
        
        for i in range(len(kx_pixel_float)):
            x_center = kx_pixel_float[i]
            y_center = ky_pixel_float[i]
            local_weights = []
            
            x_min = max(0, int(x_center - kernel_width))
            x_max = min(crop_size-1, int(x_center + kernel_width))
            y_min = max(0, int(y_center - kernel_width))
            y_max = min(crop_size-1, int(y_center + kernel_width))
            
            for y in range(y_min, y_max+1):
                for x in range(x_min, x_max+1):
                    distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
                    weight = kaiser_bessel_kernel(distance / kernel_width, width=1.1, beta=kernel_beta)
                    if weight > 0:
                        local_weights.append((y, x, weight))
            weight_map.append(local_weights)
        
        # Apply T2* and B0 effects
        spiral_kspace_with_effects = simulate_spiral_t2star_b0_effects(
            kspace_ref, t2star_slice, shimmed_b0_slice,
            kx_pixel_float, ky_pixel_float,
            time, weight_map, crop_size
        )
        
        # Grid to Cartesian and reconstruct
        cartesian_with_effects, _ = spiral_to_cartesian(
            spiral_kspace_with_effects, kx_pixel_float, ky_pixel_float, crop_size
        )
        motion_free_img_with_effects = reconstruct_with_deapod(cartesian_with_effects, crop_size)
        motion_free_with_effects[seq_name].append(motion_free_img_with_effects)
        
        # 2. Create motion-corrupted spiral k-space (WITHOUT T2*/B0 effects yet)
        print("    Creating motion-corrupted k-space...")
        spiral_kspace_motion_only = np.zeros(len(kx_pixel_float), dtype=complex)
        
        # For each k-space point, sample from appropriate frame (motion corruption only)
        for i in range(len(kx_pixel_float)):
            frame_for_this_point = frame_indices[i]
            kspace_frame = kspace_all_frames[frame_for_this_point]
            
            # Sample at this spiral location (motion corruption only)
            value = 0.0j
            total_weight = 0.0
            for y, x, weight in weight_map[i]:
                if 0 <= y < crop_size and 0 <= x < crop_size:
                    value += weight * kspace_frame[y, x]
                    total_weight += weight
            
            if total_weight > 0:
                spiral_kspace_motion_only[i] = value / total_weight
        
        # Convert motion-corrupted spiral back to Cartesian for the T2*/B0 effects function
        print("    Converting motion-corrupted spiral to Cartesian...")
        cartesian_motion_only, _ = spiral_to_cartesian(
            spiral_kspace_motion_only, kx_pixel_float, ky_pixel_float, crop_size
        )
        
        # 3. Apply T2*/B0 effects to motion-corrupted data (original B0)
        print("    Applying T2*/B0 effects to motion-corrupted data...")
        spiral_kspace_motion_with_b0 = simulate_spiral_t2star_b0_effects(
            cartesian_motion_only, t2star_slice, b0_slice,
            kx_pixel_float, ky_pixel_float,
            time, weight_map, crop_size
        )
        
        # Grid to Cartesian and reconstruct
        cartesian_motion_with_effects, _ = spiral_to_cartesian(
            spiral_kspace_motion_with_b0, kx_pixel_float, ky_pixel_float, crop_size
        )
        motion_corrupted_img_with_effects = reconstruct_with_deapod(cartesian_motion_with_effects, crop_size)
        motion_corrupted_with_effects[seq_name].append(motion_corrupted_img_with_effects)
        
        # 4. Apply T2*/shimmed B0 effects to motion-corrupted data
        print("    Applying T2*/shimmed B0 effects to motion-corrupted data...")
        spiral_kspace_motion_with_shimmed_b0 = simulate_spiral_t2star_b0_effects(
            cartesian_motion_only, t2star_slice, shimmed_b0_slice,  # Use shimmed B0
            kx_pixel_float, ky_pixel_float,
            time, weight_map, crop_size
        )
        
        # Grid to Cartesian and reconstruct
        cartesian_motion_shimmed, _ = spiral_to_cartesian(
            spiral_kspace_motion_with_shimmed_b0, kx_pixel_float, ky_pixel_float, crop_size
        )
        motion_corrupted_img_shimmed = reconstruct_with_deapod(cartesian_motion_shimmed, crop_size)
        motion_corrupted_with_shimmed_b0[seq_name].append(motion_corrupted_img_shimmed)

# Visualize comprehensive results
print("\nVisualizing results with T2* and B0 effects...")

# For each sequence, show comparison
for seq_idx, (seq_name, seq_params) in enumerate(sequences.items()):
    fig = plt.figure(figsize=(20, 15))
    fig.suptitle(f'{seq_params["description"]} - Motion + T2* + B0 Effects', fontsize=16)
    
    # Show results for middle slice
    slice_display_idx = 1  # Middle of the three processed slices
    
    # Get all versions
    motion_free_orig = motion_free_images[seq_name][0]
    motion_corrupted_orig = motion_corrupted_images[seq_name][0]
    motion_free_effects = motion_free_with_effects[seq_name][slice_display_idx]
    motion_corrupted_effects = motion_corrupted_with_effects[seq_name][slice_display_idx]
    motion_corrupted_shimmed = motion_corrupted_with_shimmed_b0[seq_name][slice_display_idx]
    
    # Get parameter maps for display (CORRECTED INDEXING)
    param_volume = param_volume_transformed
    t2star_slice = param_volume['T2star_plus'][middle_slice_idx, :, :] if 'T2star_plus' in param_volume else param_volume['T2star'][middle_slice_idx, :, :]
    b0_slice = param_volume['B0'][middle_slice_idx, :, :] if 'B0' in param_volume else np.zeros_like(t2star_slice)
    shimmed_b0_slice = param_volume['ShimmedB0'][middle_slice_idx, :, :] if 'ShimmedB0' in param_volume else b0_slice
    
    # Row 1: Parameter maps
    ax1 = plt.subplot(4, 5, 1)
    im1 = ax1.imshow(t2star_slice, cmap='hot', vmin=0, vmax=100)
    ax1.set_title('T2* Map (ms)')
    ax1.axis('off')
    plt.colorbar(im1, ax=ax1, fraction=0.046)
    
    ax2 = plt.subplot(4, 5, 2)
    im2 = ax2.imshow(b0_slice, cmap='coolwarm', vmin=-50, vmax=50)
    ax2.set_title('Original B0 (Hz)')
    ax2.axis('off')
    plt.colorbar(im2, ax=ax2, fraction=0.046)
    
    ax3 = plt.subplot(4, 5, 3)
    im3 = ax3.imshow(shimmed_b0_slice, cmap='coolwarm', vmin=-50, vmax=50)
    ax3.set_title('Shimmed B0 (Hz)')
    ax3.axis('off')
    plt.colorbar(im3, ax=ax3, fraction=0.046)
    
    ax4 = plt.subplot(4, 5, 4)
    im4 = ax4.imshow(shimmed_b0_slice - b0_slice, cmap='RdBu_r', vmin=-30, vmax=30)
    ax4.set_title('B0 Improvement')
    ax4.axis('off')
    plt.colorbar(im4, ax=ax4, fraction=0.046)
    
    # Row 2: Motion-free comparisons
    vmin, vmax = np.percentile(motion_free_orig, [1, 99])
    
    ax5 = plt.subplot(4, 5, 6)
    ax5.imshow(motion_free_orig, cmap='gray', vmin=vmin, vmax=vmax)
    ax5.set_title('Motion-Free\n(No Effects)')
    ax5.axis('off')
    
    ax6 = plt.subplot(4, 5, 7)
    ax6.imshow(motion_free_effects, cmap='gray', vmin=vmin, vmax=vmax)
    ax6.set_title('Motion-Free\n(T2* + B0)')
    ax6.axis('off')
    
    ax7 = plt.subplot(4, 5, 8)
    diff1 = np.abs(motion_free_effects - motion_free_orig)
    im7 = ax7.imshow(diff1, cmap='hot')
    ax7.set_title('Effect of T2*/B0\n(No Motion)')
    ax7.axis('off')
    plt.colorbar(im7, ax=ax7, fraction=0.046)
    
    # Row 3: Motion-corrupted comparisons
    ax10 = plt.subplot(4, 5, 11)
    ax10.imshow(motion_corrupted_orig, cmap='gray', vmin=vmin, vmax=vmax)
    ax10.set_title('Motion Only')
    ax10.axis('off')
    
    ax11 = plt.subplot(4, 5, 12)
    ax11.imshow(motion_corrupted_effects, cmap='gray', vmin=vmin, vmax=vmax)
    ax11.set_title('Motion + T2* + B0')
    ax11.axis('off')
    
    ax12 = plt.subplot(4, 5, 13)
    ax12.imshow(motion_corrupted_shimmed, cmap='gray', vmin=vmin, vmax=vmax)
    ax12.set_title('Motion + T2* + Shimmed B0')
    ax12.axis('off')
    
    ax13 = plt.subplot(4, 5, 14)
    diff2 = np.abs(motion_corrupted_effects - motion_corrupted_orig)
    im13 = ax13.imshow(diff2, cmap='hot')
    ax13.set_title('Additional Artifacts\nfrom T2*/B0')
    ax13.axis('off')
    plt.colorbar(im13, ax=ax13, fraction=0.046)
    
    ax14 = plt.subplot(4, 5, 15)
    improvement = np.abs(motion_corrupted_effects - motion_corrupted_shimmed)
    im14 = ax14.imshow(improvement, cmap='hot')
    ax14.set_title('Improvement\nfrom Shimming')
    ax14.axis('off')
    plt.colorbar(im14, ax=ax14, fraction=0.046)
    
    # Row 4: Profiles
    ax15 = plt.subplot(4, 5, 16)
    center_line = crop_size // 2
    ax15.plot(motion_free_orig[center_line, :], 'k-', label='No effects', linewidth=2)
    ax15.plot(motion_corrupted_shimmed[center_line, :], 'b--', label='With T2*/Shim', linewidth=2)
    ax15.set_title('Motion-Free Profiles')
    ax15.legend(fontsize=8)
    ax15.grid(True, alpha=0.3)
    
    ax16 = plt.subplot(4, 5, 17)
    ax16.plot(motion_corrupted_orig[center_line, :], 'k-', label='Motion only', linewidth=2)
    ax16.plot(motion_corrupted_effects[center_line, :], 'r--', label='Motion+T2*/B0', linewidth=2)
    ax16.plot(motion_corrupted_shimmed[center_line, :], 'g:', label='Motion+T2*/Shim', linewidth=2)
    ax16.set_title('Motion-Corrupted Profiles')
    ax16.legend(fontsize=8)
    ax16.grid(True, alpha=0.3)
    
    # Show all slices
    ax_slice1 = plt.subplot(4, 5, 18)
    ax_slice1.imshow(motion_corrupted_with_shimmed_b0[seq_name][0], cmap='gray')
    ax_slice1.set_title(f'Slice {slice_indices[0]}')
    ax_slice1.axis('off')
    
    ax_slice2 = plt.subplot(4, 5, 19)
    ax_slice2.imshow(motion_corrupted_with_shimmed_b0[seq_name][1], cmap='gray')
    ax_slice2.set_title(f'Slice {slice_indices[1]}')
    ax_slice2.axis('off')
    
    ax_slice3 = plt.subplot(4, 5, 20)
    ax_slice3.imshow(motion_corrupted_with_shimmed_b0[seq_name][2], cmap='gray')
    ax_slice3.set_title(f'Slice {slice_indices[2]}')
    ax_slice3.axis('off')
    
    plt.tight_layout()
    plt.show()

# Quantitative analysis
print("\nQuantitative Analysis with T2* and B0 Effects:")
print("-" * 80)

for seq_name in sequences.keys():
    print(f"\n{sequences[seq_name]['description']}:")
    
    # Use middle slice for analysis
    motion_free_orig = motion_free_images[seq_name][0]
    motion_corrupted_orig = motion_corrupted_images[seq_name][0]
    motion_free_effects = motion_free_with_effects[seq_name][1]
    motion_corrupted_effects = motion_corrupted_with_effects[seq_name][1]
    motion_corrupted_shimmed = motion_corrupted_with_shimmed_b0[seq_name][1]
    
    # Calculate metrics
    # Effect of T2*/B0 without motion
    mse_effects_only = np.mean((motion_free_effects - motion_free_orig)**2)
    psnr_effects_only = 20 * np.log10(np.max(motion_free_orig) / np.sqrt(mse_effects_only + 1e-12))
    
    # Combined effects
    mse_combined = np.mean((motion_corrupted_effects - motion_free_orig)**2)
    psnr_combined = 20 * np.log10(np.max(motion_free_orig) / np.sqrt(mse_combined + 1e-12))
    
    # With shimming
    mse_shimmed = np.mean((motion_corrupted_shimmed - motion_free_orig)**2)
    psnr_shimmed = 20 * np.log10(np.max(motion_free_orig) / np.sqrt(mse_shimmed + 1e-12))
    
    print(f"  T2*/B0 effects only (no motion):")
    print(f"    PSNR: {psnr_effects_only:.2f} dB")
    print(f"  Motion + T2*/B0 effects:")
    print(f"    PSNR: {psnr_combined:.2f} dB")
    print(f"  Motion + T2*/Shimmed B0 effects:")
    print(f"    PSNR: {psnr_shimmed:.2f} dB")
    print(f"  Improvement from shimming: {psnr_shimmed - psnr_combined:.2f} dB")
    
    # Signal loss analysis
    signal_loss_effects = np.mean(np.abs(motion_free_orig - motion_free_effects)) / np.mean(motion_free_orig) * 100
    signal_loss_combined = np.mean(np.abs(motion_free_orig - motion_corrupted_effects)) / np.mean(motion_free_orig) * 100
    signal_loss_shimmed = np.mean(np.abs(motion_free_orig - motion_corrupted_shimmed)) / np.mean(motion_free_orig) * 100
    
    print(f"  Signal loss from T2*/B0: {signal_loss_effects:.1f}%")
    print(f"  Signal loss from all effects: {signal_loss_combined:.1f}%")
    print(f"  Signal loss with shimming: {signal_loss_shimmed:.1f}%")

# Summary statistics
print("\nParameter Map Statistics (from first frame):")
param_vol = param_volume_transformed
if 'T2star' in param_vol:
    t2star_all = param_vol['T2star']
elif 'T2star_plus' in param_vol:
    t2star_all = param_vol['T2star_plus']
else:
    t2star_all = np.zeros((1,1,1))

print(f"T2* range: {np.min(t2star_all):.1f} - {np.max(t2star_all):.1f} ms")
print(f"T2* mean: {np.mean(t2star_all):.1f} ± {np.std(t2star_all):.1f} ms")

if 'B0' in param_vol:
    b0_all = param_vol['B0']
    print(f"B0 range: {np.min(b0_all):.1f} - {np.max(b0_all):.1f} Hz")
    print(f"B0 std: {np.std(b0_all):.1f} Hz")
    
    if 'ShimmedB0' in param_vol:
        shimmed_b0_all = param_vol['ShimmedB0']
        print(f"Shimmed B0 range: {np.min(shimmed_b0_all):.1f} - {np.max(shimmed_b0_all):.1f} Hz")
        print(f"Shimmed B0 std: {np.std(shimmed_b0_all):.1f} Hz")
        print(f"B0 homogeneity improvement: {(1 - np.std(shimmed_b0_all)/np.std(b0_all))*100:.1f}%")

print(f"\nReadout duration: {time[-1]*1000:.2f} ms")
print(f"Expected T2* decay at end of readout: {np.exp(-time[-1]/(np.mean(t2star_all)/1000))*100:.1f}%")

print("\nSimulation complete! All modalities processed with motion, T2*, and B0 effects.")

# import numpy as np
# import matplotlib.pyplot as plt
# from scipy.interpolate import griddata, NearestNDInterpolator
# from scipy.ndimage import gaussian_filter
# from scipy import special
# import scipy.sparse as spr
# from scipy.sparse.linalg import lsqr
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing as mp
from functools import partial
import time as timer

# Try to import CuPy for optional GPU acceleration
try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("GPU acceleration available")
except ImportError:
    GPU_AVAILABLE = False
    print("GPU not available, using CPU only")

def simulate_spiral_t2star_b0_effects_optimized(kspace_cartesian, t2star_map, b0_map, 
                                               kx_pixel_float, ky_pixel_float, 
                                               sampling_times, weight_map, image_size,
                                               use_gpu=False):
    """
    Optimized version with optional GPU acceleration
    """
    if use_gpu and GPU_AVAILABLE:
        # GPU version
        kspace_gpu = cp.asarray(kspace_cartesian, dtype=cp.complex64)
        t2star_gpu = cp.asarray(t2star_map, dtype=cp.float32)
        b0_gpu = cp.asarray(b0_map, dtype=cp.float32)
        
        # Convert to image domain
        orig_image = cp.fft.ifft2(cp.fft.ifftshift(kspace_gpu))
        
        # T2* in seconds
        t2star_sec = cp.maximum(t2star_gpu / 1000.0, 5.0 / 1000.0)
        
        # Initialize output
        spiral_kspace_with_effects = np.zeros(len(kx_pixel_float), dtype=np.complex64)
        
        # Batch process time bins
        time_bin = 0.001
        time_bins = np.arange(0, np.max(sampling_times) + time_bin, time_bin)
        
        for i, t in enumerate(time_bins[:-1]):
            time_indices = np.where((sampling_times >= t) & (sampling_times < time_bins[i+1]))[0]
            if len(time_indices) == 0:
                continue
            
            t_mid = (t + time_bins[i+1]) / 2
            decay = cp.exp(-t_mid / t2star_sec)
            b0_phase = cp.exp(-1j * 2 * cp.pi * b0_gpu * t_mid)
            
            decayed_image = orig_image * decay * b0_phase
            decayed_kspace = cp.fft.fftshift(cp.fft.fft2(decayed_image))
            decayed_kspace_cpu = cp.asnumpy(decayed_kspace)
            
            for idx in time_indices:
                value = 0.0j
                total_weight = 0.0
                for y, x, weight in weight_map[idx]:
                    if 0 <= y < image_size and 0 <= x < image_size:
                        value += weight * decayed_kspace_cpu[y, x]
                        total_weight += weight
                if total_weight > 0:
                    spiral_kspace_with_effects[idx] = value / total_weight
        
        # Clear GPU memory
        cp.get_default_memory_pool().free_all_blocks()
        
    else:
        # Original CPU version
        return simulate_spiral_t2star_b0_effects(kspace_cartesian, t2star_map, b0_map,
                                               kx_pixel_float, ky_pixel_float,
                                               sampling_times, weight_map, image_size)
    
    return spiral_kspace_with_effects

def process_single_slice(slice_data):
    """
    Process a single slice - designed to be run in parallel
    """
    # Unpack data
    (slice_idx, seq_name, seq_params, volumes_to_process_seq, param_volume,
     kx_pixel_float, ky_pixel_float, time, frame_indices, weight_map,
     crop_size, n_frames, kaiser_bessel_kernel, spiral_to_cartesian,
     reconstruct_with_deapod, use_gpu) = slice_data
    
    print(f"    Processing slice {slice_idx} for {seq_params['description']}...")
    
    # Get parameter maps for this slice
    t2star_slice = param_volume['T2star_plus'][slice_idx, :, :] if 'T2star_plus' in param_volume else param_volume['T2star'][slice_idx, :, :]
    b0_slice = param_volume['B0'][slice_idx, :, :] if 'B0' in param_volume else np.zeros((crop_size, crop_size))
    shimmed_b0_slice = param_volume['ShimmedB0'][slice_idx, :, :] if 'ShimmedB0' in param_volume else b0_slice
    
    # Get k-space data for all time frames at this slice
    kspace_all_frames = []
    
    if use_gpu and GPU_AVAILABLE:
        # Batch process all frames on GPU
        img_batch = np.zeros((n_frames, crop_size, crop_size), dtype=np.complex64)
        for frame_idx in range(n_frames):
            volume = volumes_to_process_seq[frame_idx]
            img_slice = volume[slice_idx, :, :]
            
            if img_slice.shape[0] != crop_size or img_slice.shape[1] != crop_size:
                center_y, center_x = img_slice.shape[0]//2, img_slice.shape[1]//2
                img_slice = img_slice[center_y-crop_size//2:center_y+crop_size//2,
                                     center_x-crop_size//2:center_x+crop_size//2]
            img_batch[frame_idx] = img_slice
        
        # Batch FFT on GPU
        img_batch_gpu = cp.asarray(img_batch)
        kspace_batch_gpu = cp.fft.fftshift(cp.fft.fft2(img_batch_gpu, axes=(1, 2)), axes=(1, 2))
        kspace_all_frames = [cp.asnumpy(kspace_batch_gpu[i]) for i in range(n_frames)]
        cp.get_default_memory_pool().free_all_blocks()
    else:
        # CPU processing
        for frame_idx in range(n_frames):
            volume = volumes_to_process_seq[frame_idx]
            img_slice = volume[slice_idx, :, :]
            
            if img_slice.shape[0] != crop_size or img_slice.shape[1] != crop_size:
                center_y, center_x = img_slice.shape[0]//2, img_slice.shape[1]//2
                img_slice = img_slice[center_y-crop_size//2:center_y+crop_size//2,
                                     center_x-crop_size//2:center_x+crop_size//2]
            
            kspace = np.fft.fftshift(np.fft.fft2(img_slice))
            kspace_all_frames.append(kspace)
    
    # 1. Motion-free with T2* and B0 effects
    kspace_ref = kspace_all_frames[0]
    
    spiral_kspace_with_effects = simulate_spiral_t2star_b0_effects_optimized(
        kspace_ref, t2star_slice, shimmed_b0_slice,
        kx_pixel_float, ky_pixel_float,
        time, weight_map, crop_size, use_gpu
    )
    
    cartesian_with_effects, _ = spiral_to_cartesian(
        spiral_kspace_with_effects, kx_pixel_float, ky_pixel_float, crop_size
    )
    motion_free_img_with_effects = reconstruct_with_deapod(cartesian_with_effects, crop_size)
    
    # 2. Create motion-corrupted spiral k-space (WITHOUT T2*/B0 effects yet)
    spiral_kspace_motion_only = np.zeros(len(kx_pixel_float), dtype=complex)
    
    for i in range(len(kx_pixel_float)):
        frame_for_this_point = frame_indices[i]
        kspace_frame = kspace_all_frames[frame_for_this_point]
        
        value = 0.0j
        total_weight = 0.0
        for y, x, weight in weight_map[i]:
            if 0 <= y < crop_size and 0 <= x < crop_size:
                value += weight * kspace_frame[y, x]
                total_weight += weight
        
        if total_weight > 0:
            spiral_kspace_motion_only[i] = value / total_weight
    
    # Convert motion-corrupted spiral back to Cartesian
    cartesian_motion_only, _ = spiral_to_cartesian(
        spiral_kspace_motion_only, kx_pixel_float, ky_pixel_float, crop_size
    )
    
    # 3. Apply T2*/B0 effects to motion-corrupted data (original B0)
    spiral_kspace_motion_with_b0 = simulate_spiral_t2star_b0_effects_optimized(
        cartesian_motion_only, t2star_slice, b0_slice,
        kx_pixel_float, ky_pixel_float,
        time, weight_map, crop_size, use_gpu
    )
    
    cartesian_motion_with_effects, _ = spiral_to_cartesian(
        spiral_kspace_motion_with_b0, kx_pixel_float, ky_pixel_float, crop_size
    )
    motion_corrupted_img_with_effects = reconstruct_with_deapod(cartesian_motion_with_effects, crop_size)
    
    # 4. Apply T2*/shimmed B0 effects to motion-corrupted data
    spiral_kspace_motion_with_shimmed_b0 = simulate_spiral_t2star_b0_effects_optimized(
        cartesian_motion_only, t2star_slice, shimmed_b0_slice,
        kx_pixel_float, ky_pixel_float,
        time, weight_map, crop_size, use_gpu
    )
    
    cartesian_motion_shimmed, _ = spiral_to_cartesian(
        spiral_kspace_motion_with_shimmed_b0, kx_pixel_float, ky_pixel_float, crop_size
    )
    motion_corrupted_img_shimmed = reconstruct_with_deapod(cartesian_motion_shimmed, crop_size)
    
    return (slice_idx, motion_free_img_with_effects, 
            motion_corrupted_img_with_effects, motion_corrupted_img_shimmed)

def compute_weight_map(kx_pixel_float, ky_pixel_float, crop_size, 
                      kaiser_bessel_kernel, kernel_width=1.0, kernel_beta=13.0):
    """
    Pre-compute weight map - can be reused across slices
    """
    weight_map = []
    
    for i in range(len(kx_pixel_float)):
        x_center = kx_pixel_float[i]
        y_center = ky_pixel_float[i]
        local_weights = []
        
        x_min = max(0, int(x_center - kernel_width))
        x_max = min(crop_size-1, int(x_center + kernel_width))
        y_min = max(0, int(y_center - kernel_width))
        y_max = min(crop_size-1, int(y_center + kernel_width))
        
        for y in range(y_min, y_max+1):
            for x in range(x_min, x_max+1):
                distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
                weight = kaiser_bessel_kernel(distance / kernel_width, width=1.1, beta=kernel_beta)
                if weight > 0:
                    local_weights.append((y, x, weight))
        weight_map.append(local_weights)
    
    return weight_map

# Main parallel processing function
def process_sequences_parallel(sequences, volumes_to_process, param_volume_transformed,
                             kx_pixel_float, ky_pixel_float, time, frame_indices,
                             crop_size, middle_slice_idx, n_frames,
                             kaiser_bessel_kernel, spiral_to_cartesian, 
                             reconstruct_with_deapod, simulate_spiral_t2star_b0_effects,
                             n_workers=None, use_gpu=False):
    """
    Parallel processing of sequences and slices
    
    Parameters:
    -----------
    n_workers: Number of parallel workers (None = auto-detect)
    use_gpu: Enable GPU acceleration if available
    """
    print("\nProcessing spiral acquisition with motion, T2* decay, and B0 effects (PARALLEL)...")
    print("=" * 70)
    
    start_time = timer.time()
    
    # Auto-detect number of workers
    if n_workers is None:
        n_workers = min(mp.cpu_count(), 8)  # Cap at 8 to avoid memory issues
    print(f"Using {n_workers} parallel workers")
    
    # Pre-compute weight map (same for all slices)
    print("Pre-computing weight map...")
    weight_map = compute_weight_map(kx_pixel_float, ky_pixel_float, crop_size, 
                                  kaiser_bessel_kernel)
    
    # Store results
    motion_free_with_effects = {}
    motion_corrupted_with_effects = {}
    motion_corrupted_with_shimmed_b0 = {}
    
    # Process each sequence
    for seq_name in sequences.keys():
        print(f"\nProcessing {sequences[seq_name]['description']}...")
        
        motion_free_with_effects[seq_name] = [None] * 3  # Pre-allocate
        motion_corrupted_with_effects[seq_name] = [None] * 3
        motion_corrupted_with_shimmed_b0[seq_name] = [None] * 3
        
        param_volume = param_volume_transformed
        slice_indices = [middle_slice_idx - 2, middle_slice_idx, middle_slice_idx + 2]
        
        # Prepare data for parallel processing
        slice_data_list = []
        for slice_idx in slice_indices:
            slice_data = (
                slice_idx, seq_name, sequences[seq_name], 
                volumes_to_process[seq_name], param_volume,
                kx_pixel_float, ky_pixel_float, time, frame_indices, weight_map,
                crop_size, n_frames, kaiser_bessel_kernel, spiral_to_cartesian,
                reconstruct_with_deapod, use_gpu
            )
            slice_data_list.append(slice_data)
        
        # Process slices in parallel
        if n_workers > 1:
            with ProcessPoolExecutor(max_workers=min(n_workers, len(slice_indices))) as executor:
                results = list(executor.map(process_single_slice, slice_data_list))
        else:
            # Sequential processing (useful for debugging)
            results = [process_single_slice(data) for data in slice_data_list]
        
        # Collect results
        for i, (slice_idx, motion_free, motion_corrupted, motion_shimmed) in enumerate(results):
            motion_free_with_effects[seq_name][i] = motion_free
            motion_corrupted_with_effects[seq_name][i] = motion_corrupted
            motion_corrupted_with_shimmed_b0[seq_name][i] = motion_shimmed
    
    end_time = timer.time()
    print(f"\nTotal processing time: {end_time - start_time:.2f} seconds")
    
    return motion_free_with_effects, motion_corrupted_with_effects, motion_corrupted_with_shimmed_b0

# Wrapper function to maintain compatibility with original code
def process_sequences_with_effects(sequences, volumes_to_process, param_volume_transformed,
                                 motion_free_images, motion_corrupted_images,
                                 kx_pixel_float, ky_pixel_float, time, frame_indices,
                                 crop_size, middle_slice_idx, n_frames,
                                 kaiser_bessel_kernel, spiral_to_cartesian, 
                                 reconstruct_with_deapod, simulate_spiral_t2star_b0_effects,
                                 parallel=True, n_workers=None, use_gpu=False):
    """
    Wrapper function that can switch between parallel and sequential processing
    """
    if parallel:
        return process_sequences_parallel(
            sequences, volumes_to_process, param_volume_transformed,
            kx_pixel_float, ky_pixel_float, time, frame_indices,
            crop_size, middle_slice_idx, n_frames,
            kaiser_bessel_kernel, spiral_to_cartesian, 
            reconstruct_with_deapod, simulate_spiral_t2star_b0_effects,
            n_workers, use_gpu
        )
    else:
        # Fall back to original sequential processing
        print("\nProcessing spiral acquisition with motion, T2* decay, and B0 effects...")
        print("=" * 70)
        
        # Original sequential code here...
        # (Copy your original processing code)
        pass

# Usage example:
# Replace your original processing loop with:

motion_free_with_effects, motion_corrupted_with_effects, motion_corrupted_with_shimmed_b0 = \
    process_sequences_with_effects(
        sequences, volumes_to_process, param_volume_transformed,
        motion_free_images, motion_corrupted_images,
        kx_pixel_float, ky_pixel_float, time, frame_indices,
        crop_size, middle_slice_idx, n_frames,
        kaiser_bessel_kernel, spiral_to_cartesian, 
        reconstruct_with_deapod, simulate_spiral_t2star_b0_effects,
        parallel=True,      # Enable parallel processing
        n_workers=8,        # Number of workers (None = auto)
        use_gpu=True        # Enable GPU if available
    )


# Alternative: Simple drop-in replacement for the main loop
# if __name__ == "__main__":
#     # This demonstrates how to use the parallel version
#     print("Parallel processing enabled")
#     print(f"CPU cores available: {mp.cpu_count()}")
#     if GPU_AVAILABLE:
#         print("GPU acceleration available")
#     else:
#         print("GPU not available - install cupy-cuda11x for GPU support")

# Visualize comprehensive results
print("\nVisualizing results with T2* and B0 effects...")

# For each sequence, show comparison
for seq_idx, (seq_name, seq_params) in enumerate(sequences.items()):
    fig = plt.figure(figsize=(20, 15))
    fig.suptitle(f'{seq_params["description"]} - Motion + T2* + B0 Effects', fontsize=16)
    
    # Show results for middle slice
    slice_display_idx = 1  # Middle of the three processed slices
    
    # Get all versions
    motion_free_orig = motion_free_images[seq_name][0]
    motion_corrupted_orig = motion_corrupted_images[seq_name][0]
    motion_free_effects = motion_free_with_effects[seq_name][slice_display_idx]
    motion_corrupted_effects = motion_corrupted_with_effects[seq_name][slice_display_idx]
    motion_corrupted_shimmed = motion_corrupted_with_shimmed_b0[seq_name][slice_display_idx]
    
    # Get parameter maps for display (CORRECTED INDEXING)
    param_volume = param_volume_transformed
    t2star_slice = param_volume['T2star_plus'][middle_slice_idx, :, :] if 'T2star_plus' in param_volume else param_volume['T2star'][middle_slice_idx, :, :]
    b0_slice = param_volume['B0'][middle_slice_idx, :, :] if 'B0' in param_volume else np.zeros_like(t2star_slice)
    shimmed_b0_slice = param_volume['ShimmedB0'][middle_slice_idx, :, :] if 'ShimmedB0' in param_volume else b0_slice
    
    # Row 1: Parameter maps
    ax1 = plt.subplot(4, 5, 1)
    im1 = ax1.imshow(t2star_slice, cmap='hot', vmin=0, vmax=100)
    ax1.set_title('T2* Map (ms)')
    ax1.axis('off')
    plt.colorbar(im1, ax=ax1, fraction=0.046)
    
    ax2 = plt.subplot(4, 5, 2)
    im2 = ax2.imshow(b0_slice, cmap='coolwarm', vmin=-50, vmax=50)
    ax2.set_title('Original B0 (Hz)')
    ax2.axis('off')
    plt.colorbar(im2, ax=ax2, fraction=0.046)
    
    ax3 = plt.subplot(4, 5, 3)
    im3 = ax3.imshow(shimmed_b0_slice, cmap='coolwarm', vmin=-50, vmax=50)
    ax3.set_title('Shimmed B0 (Hz)')
    ax3.axis('off')
    plt.colorbar(im3, ax=ax3, fraction=0.046)
    
    ax4 = plt.subplot(4, 5, 4)
    im4 = ax4.imshow(shimmed_b0_slice - b0_slice, cmap='RdBu_r', vmin=-30, vmax=30)
    ax4.set_title('B0 Improvement')
    ax4.axis('off')
    plt.colorbar(im4, ax=ax4, fraction=0.046)
    
    # Row 2: Motion-free comparisons
    vmin, vmax = np.percentile(motion_free_orig, [1, 99])
    
    ax5 = plt.subplot(4, 5, 6)
    ax5.imshow(motion_free_orig, cmap='gray', vmin=vmin, vmax=vmax)
    ax5.set_title('Motion-Free\n(No Effects)')
    ax5.axis('off')
    
    ax6 = plt.subplot(4, 5, 7)
    ax6.imshow(motion_free_effects, cmap='gray', vmin=vmin, vmax=vmax)
    ax6.set_title('Motion-Free\n(T2* + B0)')
    ax6.axis('off')
    
    ax7 = plt.subplot(4, 5, 8)
    diff1 = np.abs(motion_free_effects - motion_free_orig)
    im7 = ax7.imshow(diff1, cmap='hot')
    ax7.set_title('Effect of T2*/B0\n(No Motion)')
    ax7.axis('off')
    plt.colorbar(im7, ax=ax7, fraction=0.046)
    
    # Row 3: Motion-corrupted comparisons
    ax10 = plt.subplot(4, 5, 11)
    ax10.imshow(motion_corrupted_orig, cmap='gray', vmin=vmin, vmax=vmax)
    ax10.set_title('Motion Only')
    ax10.axis('off')
    
    ax11 = plt.subplot(4, 5, 12)
    ax11.imshow(motion_corrupted_effects, cmap='gray', vmin=vmin, vmax=vmax)
    ax11.set_title('Motion + T2* + B0')
    ax11.axis('off')
    
    ax12 = plt.subplot(4, 5, 13)
    ax12.imshow(motion_corrupted_shimmed, cmap='gray', vmin=vmin, vmax=vmax)
    ax12.set_title('Motion + T2* + Shimmed B0')
    ax12.axis('off')
    
    ax13 = plt.subplot(4, 5, 14)
    diff2 = np.abs(motion_corrupted_effects - motion_corrupted_orig)
    im13 = ax13.imshow(diff2, cmap='hot')
    ax13.set_title('Additional Artifacts\nfrom T2*/B0')
    ax13.axis('off')
    plt.colorbar(im13, ax=ax13, fraction=0.046)
    
    ax14 = plt.subplot(4, 5, 15)
    improvement = np.abs(motion_corrupted_effects - motion_corrupted_shimmed)
    im14 = ax14.imshow(improvement, cmap='hot')
    ax14.set_title('Improvement\nfrom Shimming')
    ax14.axis('off')
    plt.colorbar(im14, ax=ax14, fraction=0.046)
    
    # Row 4: Profiles
    ax15 = plt.subplot(4, 5, 16)
    center_line = crop_size // 2
    ax15.plot(motion_free_orig[center_line, :], 'k-', label='No effects', linewidth=2)
    ax15.plot(motion_corrupted_shimmed[center_line, :], 'b--', label='With T2*/Shim', linewidth=2)
    ax15.set_title('Motion-Free Profiles')
    ax15.legend(fontsize=8)
    ax15.grid(True, alpha=0.3)
    
    ax16 = plt.subplot(4, 5, 17)
    ax16.plot(motion_corrupted_orig[center_line, :], 'k-', label='Motion only', linewidth=2)
    ax16.plot(motion_corrupted_effects[center_line, :], 'r--', label='Motion+T2*/B0', linewidth=2)
    ax16.plot(motion_corrupted_shimmed[center_line, :], 'g:', label='Motion+T2*/Shim', linewidth=2)
    ax16.set_title('Motion-Corrupted Profiles')
    ax16.legend(fontsize=8)
    ax16.grid(True, alpha=0.3)
    
    # Show all slices
    ax_slice1 = plt.subplot(4, 5, 18)
    ax_slice1.imshow(motion_corrupted_with_shimmed_b0[seq_name][0], cmap='gray')
    ax_slice1.set_title(f'Slice {slice_indices[0]}')
    ax_slice1.axis('off')
    
    ax_slice2 = plt.subplot(4, 5, 19)
    ax_slice2.imshow(motion_corrupted_with_shimmed_b0[seq_name][1], cmap='gray')
    ax_slice2.set_title(f'Slice {slice_indices[1]}')
    ax_slice2.axis('off')
    
    ax_slice3 = plt.subplot(4, 5, 20)
    ax_slice3.imshow(motion_corrupted_with_shimmed_b0[seq_name][2], cmap='gray')
    ax_slice3.set_title(f'Slice {slice_indices[2]}')
    ax_slice3.axis('off')
    
    plt.tight_layout()
    plt.show()

# ============================================================================
# GRADIENT DELAY ANALYSIS FOR MOTION + T2* + SHIMMED B0 DATA
# This cell applies gradient delays to the already processed spiral data
# ============================================================================

import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
from scipy.ndimage import gaussian_filter, sobel, laplace
from scipy import special
import scipy.sparse as spr
from scipy.sparse.linalg import lsqr
from skimage.metrics import structural_similarity as ssim

# First, we need to recompute the spiral k-space data with effects and store it
print("Recomputing spiral k-space data with motion + T2* + shimmed B0 effects...")
print("=" * 80)

# Store spiral k-space data for each sequence and slice
spiral_kspace_data = {}

# User-configurable gradient delay parameters
delay_x_us = 1.5    # X-axis delay in microseconds
delay_y_us = 1.5    # Y-axis delay in microseconds
amp_nl = 0.0        # Amplitude non-linearity (0 = off)

# Process each sequence
for seq_name in sequences.keys():
    print(f"\nProcessing {sequences[seq_name]['description']}...")
    spiral_kspace_data[seq_name] = []
    
    # Process multiple slices
    slice_indices = [middle_slice_idx - 2, middle_slice_idx, middle_slice_idx + 2]
    
    for slice_idx in slice_indices:
        print(f"  Recomputing spiral k-space for slice {slice_idx}...")
        
        # Get parameter maps for this slice
        t2star_slice = param_volume['T2star_plus'][slice_idx, :, :] if 'T2star_plus' in param_volume else param_volume['T2star'][slice_idx, :, :]
        shimmed_b0_slice = param_volume['ShimmedB0'][slice_idx, :, :] if 'ShimmedB0' in param_volume else param_volume['B0'][slice_idx, :, :]
        
        # Get k-space data for all time frames at this slice
        kspace_all_frames = []
        for frame_idx in range(n_frames):
            volume = volumes_to_process[seq_name][frame_idx]
            img_slice = volume[slice_idx, :, :]
            
            if img_slice.shape[0] != crop_size or img_slice.shape[1] != crop_size:
                center_y, center_x = img_slice.shape[0]//2, img_slice.shape[1]//2
                img_slice = img_slice[center_y-crop_size//2:center_y+crop_size//2,
                                     center_x-crop_size//2:center_x+crop_size//2]
            
            kspace = np.fft.fftshift(np.fft.fft2(img_slice))
            kspace_all_frames.append(kspace)
        
        # Build weight map once for this slice
        weight_map = []
        kernel_width = kernel_width_pixels
        
        for i in range(len(kx_pixel_float)):
            x_center = kx_pixel_float[i]
            y_center = ky_pixel_float[i]
            local_weights = []
            
            x_min = max(0, int(x_center - kernel_width))
            x_max = min(crop_size-1, int(x_center + kernel_width))
            y_min = max(0, int(y_center - kernel_width))
            y_max = min(crop_size-1, int(y_center + kernel_width))
            
            for y in range(y_min, y_max+1):
                for x in range(x_min, x_max+1):
                    distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
                    weight = kaiser_bessel_kernel(distance / kernel_width, width=1.1, beta=kernel_beta)
                    if weight > 0:
                        local_weights.append((y, x, weight))
            weight_map.append(local_weights)
        
        # Create motion-corrupted spiral k-space
        spiral_kspace_motion_only = np.zeros(len(kx_pixel_float), dtype=complex)
        
        for i in range(len(kx_pixel_float)):
            frame_for_this_point = frame_indices[i]
            kspace_frame = kspace_all_frames[frame_for_this_point]
            
            value = 0.0j
            total_weight = 0.0
            for y, x, weight in weight_map[i]:
                if 0 <= y < crop_size and 0 <= x < crop_size:
                    value += weight * kspace_frame[y, x]
                    total_weight += weight
            
            if total_weight > 0:
                spiral_kspace_motion_only[i] = value / total_weight
        
        # Convert to Cartesian for T2*/B0 effects
        cartesian_motion_only, _ = spiral_to_cartesian(
            spiral_kspace_motion_only, kx_pixel_float, ky_pixel_float, crop_size
        )
        
        # Apply T2*/shimmed B0 effects
        spiral_kspace_motion_with_shimmed_b0 = simulate_spiral_t2star_b0_effects(
            cartesian_motion_only, t2star_slice, shimmed_b0_slice,
            kx_pixel_float, ky_pixel_float,
            time, weight_map, crop_size
        )
        
        # Store the spiral k-space data
        spiral_kspace_data[seq_name].append(spiral_kspace_motion_with_shimmed_b0)

# ============================================================================
# GRADIENT DELAY FUNCTIONS
# ============================================================================

def apply_gradient_delays(kx, ky, t, delay_x_us=0.0, delay_y_us=0.0, amp_nl=0.0):
    """Apply gradient delays to trajectory"""
    dx = delay_x_us * 1e-6
    dy = delay_y_us * 1e-6
    kx_d = np.interp(t - dx, t, kx, left=kx[0], right=kx[-1])
    ky_d = np.interp(t - dy, t, ky, left=ky[0], right=ky[-1])
    
    if amp_nl > 0:
        dkx_dt = np.gradient(kx, t)
        dky_dt = np.gradient(ky, t)
        dkx_d_dt = np.gradient(kx_d, t)
        dky_d_dt = np.gradient(ky_d, t)
        gmax = max(np.abs(dkx_dt).max(), np.abs(dky_dt).max())
        if gmax > 0:
            sf = amp_nl / gmax
            kx_d += sf * (dkx_d_dt**2) * np.sign(dkx_d_dt)
            ky_d += sf * (dky_d_dt**2) * np.sign(dky_d_dt)
    return kx_d, ky_d

def build_weight_map_and_A(kx_pix, ky_pix, N, kw, beta):
    """Build weight map and sparse encoding matrix"""
    weight_map = []
    rows, cols, vals = [], [], []
    for i in range(len(kx_pix)):
        xc, yc = kx_pix[i], ky_pix[i]
        x_lo = max(0, int(np.floor(xc - kw)))
        x_hi = min(N-1, int(np.ceil(xc + kw)))
        y_lo = max(0, int(np.floor(yc - kw)))
        y_hi = min(N-1, int(np.ceil(yc + kw)))
        local = []
        for y in range(y_lo, y_hi+1):
            for x in range(x_lo, x_hi+1):
                d = np.hypot(x - xc, y - yc) / kw
                w = kaiser_bessel_kernel(d, width=1.1, beta=beta)
                if w == 0.0:
                    continue
                idx = y * N + x
                rows.append(i); cols.append(idx); vals.append(w)
                local.append((y, x, w))
        weight_map.append(local)
    A = spr.csr_matrix((vals, (rows, cols)), shape=(len(kx_pix), N*N))
    return weight_map, A

def reconstruct_with_A(A, weight_map, spiral_samples, lambda_reg=1e-6):
    """LSQR reconstruction"""
    if len(weight_map) != len(spiral_samples):
        raise ValueError("weight_map and spiral_samples length mismatch.")
    
    Wsum = np.array([sum(w for *_, w in loc) for loc in weight_map])
    b = spiral_samples * Wsum
    sol = lsqr(A, b, damp=lambda_reg, atol=1e-8, btol=1e-8, iter_lim=10_000)[0]
    N = int(np.sqrt(sol.size))
    return sol.reshape(N, N)

def fft_recon(sparse_kspace, apod_beta=13.0, kw=1.1):
    """FFT reconstruction with apodization correction"""
    N = sparse_kspace.shape[0]
    img = np.fft.ifft2(np.fft.ifftshift(sparse_kspace))
    mag = np.abs(img)
    y, x = np.indices((N, N))
    c = (N-1)/2
    r = np.hypot(y-c, x-c) / kw
    apo = np.i0(apod_beta * np.sqrt(1 - np.minimum(r, 1)**2)) / np.i0(apod_beta)
    apo[r >= 1] = 0
    return mag / (apo + 1e-6)

def measure_blur(image):
    """Measure image sharpness using Laplacian variance"""
    laplacian = laplace(image)
    return np.var(laplacian)

# ============================================================================
# APPLY GRADIENT DELAYS AND RECONSTRUCT
# ============================================================================

print(f"\nApplying gradient delays: X={delay_x_us}μs, Y={delay_y_us}μs")
print("=" * 80)

# Store results
reconstructions_ideal = {}
reconstructions_delayed = {}
gradient_metrics = {}

fov = 0.18
# Get k_max if not defined
if 'k_max' not in globals():
    k_max = 0.5 * crop_size / fov

# Process each sequence
for seq_name in sequences.keys():
    print(f"\nProcessing {sequences[seq_name]['description']}...")
    
    reconstructions_ideal[seq_name] = []
    reconstructions_delayed[seq_name] = []
    gradient_metrics[seq_name] = []
    
    # Process each slice
    for slice_idx, spiral_kspace in enumerate(spiral_kspace_data[seq_name]):
        print(f"  Processing slice {slice_idx + 1}/3...")
        
        # Apply gradient delays to trajectory
        kx_d, ky_d = apply_gradient_delays(kx_spiral, ky_spiral, time,
                                          delay_x_us, delay_y_us, amp_nl)
        
        # Convert to pixel coordinates
        kx_pix_i = (kx_spiral / k_max) * (crop_size/2) + crop_size/2
        ky_pix_i = (ky_spiral / k_max) * (crop_size/2) + crop_size/2
        kx_pix_d = (kx_d / k_max) * (crop_size/2) + crop_size/2
        ky_pix_d = (ky_d / k_max) * (crop_size/2) + crop_size/2
        
        # Clip to image range
        kx_pix_d = np.clip(kx_pix_d, 0, crop_size-1)
        ky_pix_d = np.clip(ky_pix_d, 0, crop_size-1)
        
        # Build weight maps and encoding matrices
        wm_i, A_i = build_weight_map_and_A(kx_pix_i, ky_pix_i, crop_size, 
                                          kernel_width_pixels, kernel_beta)
        wm_d, A_d = build_weight_map_and_A(kx_pix_d, ky_pix_d, crop_size,
                                          kernel_width_pixels, kernel_beta)
        
        # Reconstruct with ideal trajectory
        sparse_i = reconstruct_with_A(A_i, wm_i, spiral_kspace, lambda_reg=1e-6)
        recon_i = fft_recon(sparse_i, kernel_beta, kernel_width_pixels)
        
        # Reconstruct with delayed trajectory
        sparse_d = reconstruct_with_A(A_d, wm_d, spiral_kspace, lambda_reg=1e-6)
        recon_d = fft_recon(sparse_d, kernel_beta, kernel_width_pixels)
        
        # Store reconstructions
        reconstructions_ideal[seq_name].append(recon_i)
        reconstructions_delayed[seq_name].append(recon_d)
        
        # Calculate metrics
        diff = recon_d - recon_i
        rmse = np.sqrt(np.mean(diff**2))
        nrmse = rmse / np.sqrt(np.mean(recon_i**2))
        
        ssim_value, _ = ssim(recon_i, recon_d, full=True,
                            data_range=recon_i.max()-recon_i.min())
        
        sharpness_ideal = measure_blur(recon_i)
        sharpness_delayed = measure_blur(recon_d)
        sharpness_ratio = sharpness_delayed / sharpness_ideal
        
        gradient_metrics[seq_name].append({
            'nrmse': nrmse,
            'ssim': ssim_value,
            'sharpness_ratio': sharpness_ratio
        })

# ============================================================================
# VISUALIZATION
# ============================================================================

print("\nVisualizing gradient delay effects on motion+T2*+B0 corrupted data...")

# For each sequence, show comparison
for seq_idx, (seq_name, seq_params) in enumerate(sequences.items()):
    fig = plt.figure(figsize=(20, 12))
    fig.suptitle(f'{seq_params["description"]} - Gradient Delay Analysis\n' + 
                 f'Delays: X={delay_x_us}μs, Y={delay_y_us}μs', fontsize=16)
    
    # Show all three slices
    for slice_idx in range(3):
        # Get data
        recon_ideal = reconstructions_ideal[seq_name][slice_idx]
        recon_delayed = reconstructions_delayed[seq_name][slice_idx]
        metrics = gradient_metrics[seq_name][slice_idx]
        
        # Reference: motion+T2*+B0 without gradient delays
        reference = motion_corrupted_with_shimmed_b0[seq_name][slice_idx]
        
        # Row for this slice
        row_offset = slice_idx * 5
        
        # Column 1: Reference (motion+T2*+B0)
        ax1 = plt.subplot(3, 5, row_offset + 1)
        vmin, vmax = np.percentile(reference, [1, 99])
        ax1.imshow(reference, cmap='gray', vmin=vmin, vmax=vmax)
        ax1.set_title(f'Slice {slice_indices[slice_idx]}\nMotion+T2*+B0')
        ax1.axis('off')
        
        # Column 2: With ideal trajectory reconstruction
        ax2 = plt.subplot(3, 5, row_offset + 2)
        ax2.imshow(recon_ideal, cmap='gray', vmin=vmin, vmax=vmax)
        ax2.set_title('Ideal Trajectory\nReconstruction')
        ax2.axis('off')
        
        # Column 3: With delayed trajectory
        ax3 = plt.subplot(3, 5, row_offset + 3)
        ax3.imshow(recon_delayed, cmap='gray', vmin=vmin, vmax=vmax)
        ax3.set_title(f'Delayed Trajectory\nNRMSE={metrics["nrmse"]:.3f}')
        ax3.axis('off')
        
        # Column 4: Difference map
        ax4 = plt.subplot(3, 5, row_offset + 4)
        diff_map = np.abs(recon_delayed - recon_ideal)
        im4 = ax4.imshow(diff_map, cmap='hot', vmin=0, vmax=0.1*vmax)
        ax4.set_title(f'Gradient Delay\nArtifacts')
        ax4.axis('off')
        plt.colorbar(im4, ax=ax4, fraction=0.046)
        
        # Column 5: Line profiles
        ax5 = plt.subplot(3, 5, row_offset + 5)
        center = crop_size // 2
        ax5.plot(recon_ideal[center, :], 'b-', linewidth=2, label='Ideal')
        ax5.plot(recon_delayed[center, :], 'r--', linewidth=2, label='Delayed')
        ax5.set_title(f'SSIM={metrics["ssim"]:.3f}\nSharp={metrics["sharpness_ratio"]:.3f}')
        ax5.legend(fontsize=8)
        ax5.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# ============================================================================
# SUMMARY STATISTICS
# ============================================================================

print("\nGradient Delay Impact Summary:")
print("=" * 80)
print(f"Delay settings: X={delay_x_us}μs, Y={delay_y_us}μs")
print("\nMetrics averaged across slices:")

for seq_name in sequences.keys():
    print(f"\n{sequences[seq_name]['description']}:")
    
    # Average metrics across slices
    avg_nrmse = np.mean([m['nrmse'] for m in gradient_metrics[seq_name]])
    avg_ssim = np.mean([m['ssim'] for m in gradient_metrics[seq_name]])
    avg_sharp = np.mean([m['sharpness_ratio'] for m in gradient_metrics[seq_name]])
    
    print(f"  NRMSE: {avg_nrmse:.4f} ({avg_nrmse*100:.2f}%)")
    print(f"  SSIM: {avg_ssim:.4f}")
    print(f"  Sharpness ratio: {avg_sharp:.4f}")
    
    # Quality assessment
    if avg_nrmse < 0.05 and avg_ssim > 0.95 and avg_sharp > 0.9:
        print("  Assessment: ✓ Excellent - Minimal impact")
    elif avg_nrmse < 0.10 and avg_ssim > 0.90 and avg_sharp > 0.8:
        print("  Assessment: ⚠ Good - Minor artifacts")
    elif avg_nrmse < 0.15 and avg_ssim > 0.85 and avg_sharp > 0.7:
        print("  Assessment: ⚠ Fair - Noticeable artifacts")
    else:
        print("  Assessment: ✗ Poor - Significant artifacts")

# Trajectory error analysis
print("\nTrajectory Error Analysis:")
traj_error = np.sqrt((kx_d - kx_spiral)**2 + (ky_d - ky_spiral)**2)
print(f"  Max trajectory error: {np.max(traj_error):.3f} cycles/m")
print(f"  Mean trajectory error: {np.mean(traj_error):.3f} cycles/m")
print(f"  Error at k-space edge: {traj_error[-1]:.3f} cycles/m")

print("\nGradient delay analysis complete!")